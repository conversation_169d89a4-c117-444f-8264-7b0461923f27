<template>
  <div class="size-full flex justify-between items-center pb-2">
    <!-- <div class="w-1/4 h-full py-3 bg-primary border-r-2 border-[#e1e1e1]">
      <div class="flex justify-between items-center px-1">
        <button
          @click="() => emit('close')"
          class="size-8 flex justify-start items-center gap-1 text-secondary-500 hover:text-secondary-600 transition-all duration-200"
        >
          <ChevronLeftIcon class="size-7" />
        </button>
        <div class="text-lg font-semibold text-secondary-700">Availability</div>
        <div
          class="w-8 text-secondary-700 hover:text-secondary-900 cursor-pointer transition-all duration-300"
          @click="
            selSchedule = defaultSelSchedule;
            editableSch = { ...defaultSelSchedule };
          "
        >
          <PencilIcon class="size-4" />
        </div>
      </div>
      <div class="flex flex-col gap-2 w-full h-auto px-1 mt-4">
        <div
          class="group flex justify-between items-center px-1 rounded-md w-full h-14 hover:bg-primary-600/50 transition-all duration-300 cursor-pointer"
          v-for="schedule in schedules"
          :class="editableSch.id == schedule.id ? 'bg-secondary-300 text-secondary-800' : 'bg-primary-600/30'"
          @click="() => selectSchedule(schedule)"
        >
          <div class="flex flex-col gap-1">
            <div>{{ schedule.name }}</div>
            <div class="flex items-center justify-between w-full gap-4">
              <div class="flex gap-1 items-center text-xs">
                <GlobeAltIcon class="size-4" />
                {{ schedule.timeZone }}
              </div>
              <div
                class="text-xs bg-dark-400 text-white px-1 py-[1px] rounded"
                v-if="session.calInfo?.defaultScheduleId === schedule.id"
              >
                default
              </div>
            </div>
          </div>
          <div class="group-hover:flex hidden h-full items-center w-7">
            <button
              @click="() => deleteSchedule(schedule)"
              class="bg-red-200 p-1 rounded-lg text-red-600 hover:bg-red-300 transition-all duration-300"
            >
              <TrashIcon class="size-5" />
            </button>
          </div>
        </div>
      </div>
    </div> -->

    <SideBar
      :schedules="schedules"
      @close="() => emit('close')"
      @select-schedule="(s:CalSchedule)=>selectSchedule(s)"
      @delete-schedule="(s:CalSchedule)=>deleteSchedule(s)"
      @new-schedule="
        selSchedule = defaultSelSchedule;
        editableSch = { ...defaultSelSchedule };
      "
    />

    <div class="w-3/4 h-full py-2 px-4 flex flex-col gap-2 overflow-y-auto custom-scrollbar">
      <div class="w-full flex justify-between items-center">
        <div class="w-fit text-2xl font-semibold text-secondary-700">
          <div
            class="flex gap-2 items-center group"
            v-if="!nameOnEdit && editableSch?.name"
            @dblclick="nameOnEdit = true"
          >
            <div>{{ editableSch?.name }}</div>
            <div class="group-hover:flex hidden p-1 rounded bg-primary cursor-pointer" @click="nameOnEdit = true">
              <PencilIcon class="size-4" />
            </div>
            <div
              class="text-base bg-secondary-600 text-white px-2 py-[1px] rounded"
              v-if="session.calInfo?.defaultScheduleId === editableSch.id"
            >
              default
            </div>
          </div>
          <div v-else class="relative flex items-center gap-1">
            <div class="relative">
              <input
                class="bg-primary-300/40 p-0 border-primary-300 pl-1 text-2xl border-2 rounded"
                type="text"
                v-model="editableSch.name"
                name="name"
                autofocus
              />
              <kbd class="absolute right-1 top-2 text-xs bg-primary-100 p-0.5 px-1 rounded text-primary-500">Enter</kbd>
            </div>
            <button class="size-7 text-green-500" @click="nameOnEdit = false">
              <CheckIcon class="size-7" />
            </button>
            <button
              class="size-7 text-red-500"
              @click="
                editableSch.name = selSchedule.name;
                nameOnEdit = false;
              "
            >
              <XMarkIcon class="size-7" />
            </button>
          </div>
        </div>
        <div class="w-60" :key="editableSch.name">
          <TimeZoneSelect :value="editableSch.timeZone" @select="(value:string)=> editableSch.timeZone = value" />
        </div>
      </div>
      <div class="w-full flex-1 mt-5" :key="editableSch.name">
        <div class="flex flex-col gap-3">
          <div
            class="w-full min-h-12 bg-primary border-1 border-black/10 drop-shadow px-2 rounded-md flex justify-between items-start py-2"
            v-for="(value, index) of DAYS"
            :key="editableSch.name"
          >
            <div class="flex gap-4 items-center mt-1">
              <div>
                <ToggleButton
                  :id="`${value}-${index}`"
                  :value="dayActive(index)"
                  @changed="(val:boolean) => ToggleDayState(index, val)"
                />
              </div>
              <div>{{ value }}</div>
            </div>
            <div class="z-50 flex flex-col gap-2">
              <div
                class="flex items-center gap-3"
                v-if="dayActive(index) && availability.days[index]"
                v-for="(av, i) of availability.days[index]"
                :key="i"
              >
                <div class="flex gap-3">
                  <TimeSelector
                    :value="av.startTime"
                    @select="(value:string)=>{
                      modifyTime(index,av.startTime,av.endTime,value,av.endTime)
                    }"
                  />
                  -
                  <TimeSelector
                    :value="av.endTime"
                    @select="(value:string)=>{
                      modifyTime(index,av.startTime,av.endTime,av.startTime,value)
                    }"
                  />
                </div>
                <div
                  class="size-6 rounded flex justify-center items-center cursor-pointer transition-all duration-200"
                  :class="
                    i <= 0
                      ? 'bg-secondary-300 text-secondary-700 hover:bg-secondary-400'
                      : 'bg-red-200 text-red-700 hover:bg-red-300'
                  "
                  @click="() => (i <= 0 ? createNewTime(index) : removeTime(index, av.startTime, av.endTime))"
                >
                  <PlusIcon v-if="i <= 0" class="size-4" />
                  <TrashIcon v-else class="size-4" />
                </div>
              </div>
              <div class="flex gap-3" v-else-if="dayActive(index) && !availability.days[index]">
                <div class="flex gap-3">
                  <TimeSelector :value="'12:00 PM'" />
                  -
                  <TimeSelector :value="'12:00 PM'" />
                </div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-end items-center mt-5 gap-4">
        <button
          @click="setScheduleAsDefault"
          v-if="session.calInfo?.defaultScheduleId !== editableSch.id"
          class="flex justify-center items-center bg-primary-200 text-primary-800 w-40 py-1.5 rounded-md hover:bg-secondary-600 transition-all duration-200"
        >
          Set as default
        </button>
        <button
          :key="state"
          @click="() => (selSchedule.id === 0 ? createSchedule() : saveSchedule())"
          class="flex justify-center items-center bg-secondary-800 text-white w-40 py-1.5 rounded-md hover:bg-dark-500 transition-all duration-200"
        >
          <div v-if="state === 'none'">{{ selSchedule.id == 0 ? "Create" : "Save" }}</div>
          <div v-else-if="state === 'loading'"><ArrowPathIcon class="size-5 animate-spin" /></div>
          <div v-else-if="state === 'done'"><CheckIcon class="size-5" /></div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { CalSchedule, defaultSelSchedule } from "../../../models/schedule-model";
import { calSchedulesService } from "../../../services/schedules-cal-service";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { ArrowPathIcon, CheckIcon, PencilIcon, PlusIcon, TrashIcon, XMarkIcon } from "@heroicons/vue/24/outline";
import ToggleButton from "../../../components/ui/ToggleButton.vue";
import {
  addDayToAvailability,
  AvailabilityChanges,
  AvailGroups,
  compareScheduleAvailabilities,
  groupAvailability,
  manageDayAvailability,
  moveDayAvailability,
  removeDayFromAvailability,
  timeToISOString,
} from "../../../utils/availability-utils";
import { CalAvailability, DAYS } from "../../../models/availability-model";
import TimeSelector from "../../../components/ui/TimeSelector.vue";
import { calAvailabilityService } from "../../../services/availablity-cal-service";
import { ask } from "@tauri-apps/plugin-dialog";
import { calUsersService } from "../../../services/user-cal-service";
import SideBar from "./SideBar.vue";
import TimeZoneSelect from "../../../components/ui/TimeZoneSelect.vue";
// import { DAYS } from "../../models/availability-model";

const emit = defineEmits(["close"]);

const schedules = ref<CalSchedule[]>([]);
const selSchedule = ref<CalSchedule>(defaultSelSchedule);
const session = useCurrentUserStore();
const selTimeZone = computed(() => selSchedule.value.timeZone);
const nameOnEdit = ref(false);
const editableSch = ref<CalSchedule>(defaultSelSchedule);
const availability = computed<AvailGroups>(() => {
  //  console.log("SELECTED SCHEDULE", editableSch.value);
  if (editableSch.value) return groupAvailability(editableSch.value.availability);
  return { days: {}, dates: {} };
});

async function getSchedules() {
  if (session.calInfo?.id) schedules.value = (await calSchedulesService.getUserSchedules(session.calInfo?.id)) ?? [];
  console.log("🚀 ~ getSchedules ~ schedules.value:", schedules.value);
}

function selectSchedule(s: CalSchedule) {
  selSchedule.value = s;
  editableSch.value = { ...s };
}

function ToggleDayState(day: number, active: boolean) {
  const result = manageDayAvailability(editableSch.value, day, active);
  // //  console.log(result);
  editableSch.value = result;
}

function createNewTime(day: number) {
  const result = addDayToAvailability(editableSch.value, day, "none", "none");
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function removeTime(day: number, start: string, end: string) {
  const result = removeDayFromAvailability(editableSch.value, day, start, end);
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function modifyTime(day: number, oldStart: string, oldEnd: string, newStart: string, newEnd: string) {
  const result = moveDayAvailability(editableSch.value, day, oldStart, oldEnd, newStart, newEnd);
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function dayActive(index: number) {
  const active = Object.keys(availability.value.days ?? []).includes(index.toString());
  // //  console.log(`${index} Day ${DAYS[index]} is Active ? ${active}`);
  return active;
}

const state = ref<"none" | "loading" | "done">("none");

async function deleteSchedule(sch: CalSchedule) {
  const cnf = await ask("This action cannot be reverted. Are you sure?", {
    title: "Delete schedule",
  });
  if (cnf && sch.id) {
    await calSchedulesService.deleteSchedule(sch.id);
    await getSchedules();
  }
}

async function setScheduleAsDefault() {
  await calUsersService.updateCurrentUser({ defaultScheduleId: editableSch.value.id }).then(async () => {
    // await session.updateCurrentCalUser();
    await getSchedules();
    editableSch.value = { ...editableSch.value };
  });
}

async function saveSchedule() {
  state.value = "loading";
  const changes: AvailabilityChanges = compareScheduleAvailabilities(selSchedule.value, editableSch.value);
  //  console.log("###### START ######", changes);
  changes.toCreate.forEach(async (change) => {
    state.value = "loading";
    let obj: CalAvailability = {
      ...change,
      endTime: timeToISOString(change.endTime),
      startTime: timeToISOString(change.startTime),
    };
    await calAvailabilityService.createAvailability(obj);
    state.value = "done";
  });

  changes.toUpdate.forEach(async (change) => {
    state.value = "loading";
    let obj: CalAvailability = {
      ...change,
      endTime: timeToISOString(change.endTime),
      startTime: timeToISOString(change.startTime),
    };
    delete obj.date;
    delete obj.eventTypeId;
    await calAvailabilityService.updateAvailability(obj.id!, obj);
    state.value = "done";
  });

  changes.toRemove.forEach(async (change) => {
    state.value = "loading";
    await calAvailabilityService.removeAvailability(change.id!);
    state.value = "done";
  });

  if (editableSch.value.name !== selSchedule.value.name || editableSch.value.timeZone !== selSchedule.value.timeZone) {
    state.value = "loading";
    await calSchedulesService.updateSchedule(selSchedule.value.id!, {
      name: editableSch.value.name ?? selSchedule.value.name,
      timeZone: editableSch.value.timeZone ?? selSchedule.value.timeZone,
    });
    state.value = "done";
  }

  setTimeout(async () => {
    state.value = "none";
    await getSchedules();
  }, 2000);
  //  console.log("###### END ######");
}

async function createSchedule() {
  //  console.log("New Schedule:");
  //  console.log("Name:", editableSch.value.name);
  //  console.log("Time Zone:", editableSch.value.timeZone);
  //  console.log("Calendar Info ID:", session.calInfo?.id);

  // Combined output for clarity
  //  console.log("New Schedule (Combined):", editableSch.value.name && editableSch.value.timeZone && session.calInfo?.id);

  if (editableSch.value.name && editableSch.value.timeZone && session.calInfo?.id) {
    state.value = "loading";
    const newSch = await calSchedulesService.createSchedule({
      name: editableSch.value.name,
      timeZone: editableSch.value.timeZone,
      userId: session.calInfo?.id,
    });
    //  console.log("New Schedule", newSch);
    if (newSch) {
      //  console.log("YAY");
      editableSch.value.id = newSch.id;
      selSchedule.value = newSch;
      //editableSch.value.availability = editableSch.value.availability.map((av) => ({ ...av, scheduleId: newSch.id }));
      for (const av of newSch.availability) {
        if (av.id) await calAvailabilityService.removeAvailability(av.id);
      }
      //  console.log("editable ====>", editableSch.value.availability, "schID", newSch.id);
      for (const av of editableSch.value.availability) {
        const obj: CalAvailability = {
          ...av,
          scheduleId: newSch.id,
          startTime: timeToISOString(av.startTime),
          endTime: timeToISOString(av.endTime),
        };
        //  console.log("AV=>", obj);
        const res = await calAvailabilityService.createAvailability(obj);
        //  console.log("RES=>", res);
      }
    }
    state.value = "done";
    setTimeout(async () => {
      state.value = "none";
      await getSchedules();
    }, 2000);
  }
}

watch(
  selSchedule,
  (value) => {
    // //  console.log("Selected Schedule", value);
  },
  { deep: true }
);

onMounted(async () => {
  await getSchedules();
});
</script>

<style scoped></style>
