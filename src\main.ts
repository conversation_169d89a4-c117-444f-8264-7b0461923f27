import { createApp } from "vue";
import "primeicons/primeicons.css";
import "./style.css";
import "flag-icons/css/flag-icons.min.css";
import App from "./App.vue";
import CheckUser from "./pages/CheckUserPage.vue";
import MainPageVue from "./pages/MainPage.vue";
import SettingsPage from "./pages/settings/SettingsPage.vue";
import PricingPage from "./pages/PricingPage.vue";
import AuthPage from "./pages/auth/AuthPage.vue";

import CreateAssistantPage from "./pages/CreateAssistantPage.vue";
import EditAssistantPage from "./pages/EditAssistantPage.vue";
import AssistantsPage from "./pages/AssistantsPage.vue";
import SignInPage from "./pages/auth/SignInPage.vue";
import OnBoarding from "./pages/auth/OnBoarding.vue";

import { createRouter, createWebHistory } from "vue-router";
import { createPinia } from "pinia";
import UserProfilePage from "./pages/UserProfilePage.vue";
import SendPage from "./pages/SendPage.vue";
import SubscriptionGate from "./pages/SubscriptionGate.vue";
import GoogleSignInPlugin from "vue3-google-login";
import Emails from "./pages/EmailPage.vue"; // Adjust the path according to your project structure
import Calendar from "./pages/CalenderPage.vue"; // Adjust the path according to your project structure
import Operator from "./pages/OperatorPage.vue"; // Adjust the path according to your project structure
import PasswordPage from "./pages/auth/PasswordPage.vue"; // Adjust the path according to your project structure
// import 'vuetify/styles'
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";

const routes = [
  // Entry point, decide where to go (mainPage or signIn) based on logic
  // { path: "/", component: CheckUser },

  // Main Page with its own set of children routes
  {
    path: "/",
    component: CheckUser,
    children: [
      {
        path: "",
        redirect: "emails", // Ensure this redirects correctly within mainPage
      },
      {
        path: "emails",
        component: Emails,
      },
      {
        path: "calendar",
        component: Calendar,
      },
      {
        path: "operator",
        component: Operator,
      },
      { path: "send", component: SendPage },
      { path: "userprofile", component: UserProfilePage },
      {
        path: "auth",
        component: AuthPage,
        children: [
          { path: "", redirect: "signin" },
          { path: "signin", component: SignInPage },
          { path: "password", component: PasswordPage },
          {
            path: "onboarding",
            component: OnBoarding,
          },
        ]
      },
      // { path: "/subscribe", component: SubscriptionGate },
    ],
  },

  // SignIn Page - should be separate from aliases to avoid conflicts

  // UserProfile Page, should be accessed from mainPage or directly
  // { path: "/userprofile", component: UserProfilePage },

  // Additional routes
  { path: "/settings", component: SettingsPage },
  { path: "/pricings", component: PricingPage },
  { path: "/subscribe", component: SubscriptionGate },

  { path: "/assistants", component: AssistantsPage },
  { path: "/assistants/create", component: CreateAssistantPage },
  { path: "/assistants/:id", component: EditAssistantPage },

  // Fallback for unmatched routes (optional)
  { path: "/:pathMatch(.*)*", redirect: "/" },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const pinia = createPinia();

const myCustomLightTheme = {
  dark: false,
  colors: {
    background: "#FFFFFF",
    surface: "#FFFFFF",
    "surface-bright": "#FFFFFF",
    "surface-light": "#EEEEEE",
    "surface-variant": "#424242",
    "on-surface-variant": "#EEEEEE",
    primary: "#ede6dc",
    "primary-darken-1": "#1F5592",
    secondary: "#48A9A6",
    "secondary-darken-1": "#018786",
    error: "#B00020",
    info: "#2196F3",
    success: "#4CAF50",
    warning: "#FB8C00",
  },
  variables: {
    "border-color": "#000000",
    "border-opacity": 0.12,
    "high-emphasis-opacity": 0.87,
    "medium-emphasis-opacity": 0.6,
    "disabled-opacity": 0.38,
    "idle-opacity": 0.04,
    "hover-opacity": 0.04,
    "focus-opacity": 0.12,
    "selected-opacity": 0.08,
    "activated-opacity": 0.12,
    "pressed-opacity": 0.12,
    "dragged-opacity": 0.08,
    "theme-kbd": "#212529",
    "theme-on-kbd": "#FFFFFF",
    "theme-code": "#F5F5F5",
    "theme-on-code": "#000000",
  },
};

const vuetify = createVuetify({
  components,
  directives,
  theme: { defaultTheme: "myCustomLightTheme", themes: { myCustomLightTheme } },
});

createApp(App)
  .use(router)
  .use(pinia)
  .use(vuetify)
  .use(GoogleSignInPlugin, {
    clientId: "834830517259-urgd6md3v79ccksbq1ki1ar1cb8laa1r.apps.googleusercontent.com",
  })
  .mount("#app");
