use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::fetch_emails::EmailSummary;
use crate::models::app_data::AppData;
use crate::models::email_category::{ EmailCategory, NewEmailCategory };
use crate::schema::emails::{ self, full_domain };
use crate::schema::emails::{ dsl, thread_id };
use crate::{ db::establish_db_connection, db::get_pooled_connection, models::email::Email };
use chrono::NaiveDate;
use diesel::dsl::{ count, date };
use diesel::prelude::*;
use diesel::result::Error;
use diesel::Connection;
use reqwest::Client;
use serde::{ Deserialize, Serialize };
use serde_json::json;
use tokio::sync::RwLock;
use tracing::info;
use std::collections::HashMap;
use std::collections::HashSet;
use std::sync::Arc;
use std::thread;
use std::time::Duration;
use uuid::Uuid;

use chrono::{ DateTime, NaiveDateTime, Utc };

// pub fn list_emails(category_id: &String, full_child_domains: &String) -> Result<Vec<Email>, Error> {
//     let connection = &mut establish_db_connection();

//     // Initialize the vector that will store all fetched emails
//     let mut all_emails: Vec<Email> = Vec::new();
//     // println!("Child domains category: {:?}", full_child_domains);

//     // Check if `full_child_domains` exists and is not empty
//     if let domains = full_child_domains {
//         // Split the full_child_domains by commas into a list of category IDs
//         let category_ids: Vec<&str> = domains.split(',').map(|id| id.trim()).collect();

//         // Loop through each category ID and fetch emails
//         for id in category_ids {
//             let result = connection.transaction::<_, Error, _>(|conn| {
//                 dsl::emails
//                     .filter(dsl::category.eq(id))
//                     .order_by(dsl::date.desc())
//                     .load::<Email>(conn)
//             });

//             // If fetching emails for this category ID succeeded, append to the email list
//             match result {
//                 Ok(mut emails) => {
//                     all_emails.append(&mut emails);
//                 }
//                 Err(ref e) => {
//                     // Log the error and retry the transaction
//                     println!("Transaction failed for category {} due to: {:?}", id, e);
//                     let retry_result = connection.transaction::<_, Error, _>(|conn| {
//                         dsl::emails
//                             .filter(dsl::category.eq(id))
//                             .order_by(dsl::date.desc())
//                             .load::<Email>(conn)
//                     });

//                     if let Ok(mut retry_emails) = retry_result {
//                         all_emails.append(&mut retry_emails);
//                     } else {
//                         println!("Retry failed for category {}", id);
//                     }
//                 }
//             }
//         }
//     }

//     // Fetch emails for the primary category_id passed as an argument
//     let primary_result = connection.transaction::<_, Error, _>(|conn| {
//         dsl::emails
//             .filter(dsl::category.eq(category_id))
//             .order_by(dsl::date.desc())
//             .load::<Email>(conn)
//     });

//     // Append emails from the primary category if successful
//     match primary_result {
//         Ok(mut primary_emails) => {
//             all_emails.append(&mut primary_emails);
//         }
//         Err(ref e) => {
//             println!("Transaction failed for primary category {} due to: {:?}", category_id, e);
//             let retry_result = connection.transaction::<_, Error, _>(|conn| {
//                 dsl::emails
//                     .filter(dsl::category.eq(category_id))
//                     .order_by(dsl::date.desc())
//                     .load::<Email>(conn)
//             });

//             if let Ok(mut retry_emails) = retry_result {
//                 all_emails.append(&mut retry_emails);
//             } else {
//                 println!("Retry failed for primary category {}", category_id);
//             }
//         }
//     }

//     // Return the complete list of emails
//     Ok(all_emails)
// }

pub fn group_emails_by_thread_sorted(emails: &[Email]) -> Vec<Vec<Email>> {
    let mut thread_map: HashMap<String, Vec<Email>> = HashMap::new();

    // Group emails by thread_id
    for email in emails {
        let th_id = email.clone().thread_id;
        if let Some(thr_id) = th_id {
            thread_map.entry(thr_id).or_insert_with(Vec::new).push(email.clone());
        } else {
            thread_map.entry(email.clone().id).or_insert_with(Vec::new).push(email.clone());
        }
    }

    // Sort emails within each thread by date
    for emails in thread_map.values_mut() {
        emails.sort_by(|a, b| a.date.cmp(&b.date));
    }

    // Convert the HashMap values into a Vec<Vec<Email>>
    thread_map.into_values().collect()
}

pub fn get_emails_by_categorie_id(
    id: String,
    search_query: Option<SearchQuery>,
    page: usize,
    per_page: usize,
    filters: Option<Filters>
) -> Result<Vec<Email>, diesel::result::Error> {
    // let connection = &mut establish_db_connection();
    let mut conn = get_pooled_connection();

    let fixed_categories = [
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters",
    ];
    conn.transaction::<_, Error, _>(|conn| {
        // let mut query = dsl::emails.filter(dsl::category.eq(id)).into_boxed();
        let mut query = if fixed_categories.contains(&id.as_str()) {
            dsl::emails.filter(dsl::storage_location.eq(id.clone())).into_boxed()
        } else {
            dsl::emails.filter(dsl::category.eq(id.clone())).into_boxed()
        };
        if let Some(sq) = &search_query {
            if let Some(sender) = &sq.sender {
                let pattern = format!("%{}%", sender);
                query = query.filter(dsl::from.like(pattern));
            }
            if let Some(recipient) = &sq.recipient {
                let pattern = format!("%{}%", recipient);
                query = query.filter(dsl::to.like(pattern));
            }
            if let Some(body) = &sq.body {
                let pattern = format!("%{}%", body);
                query = query.filter(dsl::snippet.like(pattern));
            }
            if let Some(subject) = &sq.subject {
                let pattern = format!("%{}%", subject);
                query = query.filter(dsl::subject.like(pattern));
            }
        }

        if let Some(f) = &filters {
            if let Some(category) = &f.sub_category {
                let pattern = format!("%{}%", category);
                println!("sub category => {:?}", pattern);
                query = query.filter(dsl::from.like(pattern));
            }
        }

        query
            .order_by(dsl::date.desc())
            .limit(per_page as i64) // Apply pagination
            .offset(((page - 1) * per_page) as i64) // Offset calculation
            .load::<Email>(conn)
    })
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchQuery {
    pub sender: Option<String>,
    pub recipient: Option<String>,
    pub subject: Option<String>,
    pub body: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Filters {
    pub sub_category: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EmailsOutput {
    pub length: usize,
    pub data: Vec<Vec<Email>>,
}

pub fn list_emails(
    category_id: &String,
    full_child_domains: &String,
    page: usize,
    per_page: usize,
    search_query: Option<SearchQuery>,
    filters: Option<Filters>
) -> Result<EmailsOutput, Error> {
    let _conn: r2d2::PooledConnection<diesel::r2d2::ConnectionManager<SqliteConnection>> = get_pooled_connection(); // ⚠️ No longer used here directly

    // let connection = &mut establish_db_connection();
    println!(
        "full child domains {:?}, page: {:?} per page: {:?}",
        full_child_domains,
        page,
        per_page
    );
    // Initialize the vector that will store all fetched emails
    let mut all_emails: Vec<Email> = Vec::new();
    let mut emails_count = 0;

    // Handle child domains
    if let domains = full_child_domains {
        let category_ids: Vec<&str> = domains
            .split(',')
            .map(|id| id.trim())
            .collect();

        for id in category_ids {
            let result = get_emails_by_categorie_id(
                id.to_string(),
                search_query.clone(),
                page,
                per_page,
                filters.clone()
            );

            match result {
                Ok(mut emails) => {
                    emails_count += emails.len();
                    all_emails.append(&mut emails);
                }
                Err(ref e) => {
                    println!("Transaction failed for category {}: {:?}", id, e);
                    let retry_result = get_emails_by_categorie_id(
                        id.to_string(),
                        search_query.clone(),
                        page,
                        per_page,
                        filters.clone()
                    );

                    if let Ok(mut retry_emails) = retry_result {
                        emails_count += retry_emails.len();
                        all_emails.append(&mut retry_emails);
                    }
                }
            }
        }
    }

    // Handle primary category_id
    let primary_result = get_emails_by_categorie_id(
        category_id.to_string(),
        search_query.clone(),
        page,
        per_page,
        filters.clone()
    );

    match primary_result {
        Ok(mut primary_emails) => {
            emails_count += primary_emails.len();
            all_emails.append(&mut primary_emails);
        }
        Err(ref e) => {
            println!("Transaction failed for primary category {}: {:?}", category_id, e);
            let retry_result = get_emails_by_categorie_id(
                category_id.to_string(),
                search_query.clone(),
                page,
                per_page,
                filters.clone()
            );

            if let Ok(mut retry_emails) = retry_result {
                emails_count += retry_emails.len();
                all_emails.append(&mut retry_emails);
            }
        }
    }
    println!("emails count => {:?}", emails_count);
    // Return the complete list of emails

    Ok(EmailsOutput {
        length: emails_count,
        data: group_emails_by_thread_sorted(&all_emails),
    })
}

pub fn get_emails_by_thread_id(id: String) -> Result<Vec<Email>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Initialize the vector that will store all fetched emails
    let all_emails = dsl::emails
        .filter(thread_id.eq(id))
        .order_by(dsl::date.asc())
        .load::<Email>(connection);
    all_emails
}

pub async fn update_processed_email(
    email_id: &str,
    thread_summary: Option<String>,
    urgency_score: Option<i32>,
    sentiment: Option<String>,
    actionable_items: String,
    attachment_types: String,
    language: String,
    classification: String // <-- NEW
) -> Result<(), Error> {
    // println!("Starting the update_processed_email function...");

    // // Establish database connection
    // println!("Database connection established.");

    // // Logging inputs
    // println!("Thread Summary: {:?}", thread_summary);
    // println!("Urgency Score: {:?}", urgency_score);
    // println!("Sentiment: {:?}", sentiment);
    // println!("Actionable Items: {}", actionable_items);
    // println!("Attachment Types: {}", attachment_types);
    // println!("Language: {}", language);

    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Update the database with the processed results
    connection.transaction::<_, Error, _>(|conn| {
        diesel
            ::update(dsl::emails.find(email_id))
            .set((
                dsl::thread_summary.eq(thread_summary.unwrap_or_default()),
                dsl::urgency_score.eq(urgency_score.unwrap_or(0)),
                dsl::sentiment.eq(sentiment.unwrap_or_default()),
                dsl::actionable_items.eq(actionable_items),
                dsl::attachment_types.eq(attachment_types),
                dsl::language.eq(language),
                dsl::process_flag.eq(false), // Mark email as processed
                dsl::storage_location.eq(classification),
            ))
            .execute(conn)
    })?;
    Ok(())
}

pub async fn save_email_summary(email: EmailSummary) -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(30)).build()?;

    let api_url = "http://***************:8080/api/email-summaries";
    // Prepare the body separately so we can print it
    let body =
        serde_json::json!({
    "id": email.id,
    "userId": email.user_id,
    "subject": email.subject,
    "snippet": email.snippet,
    "threadId": email.thread_id,
    "date": email.date,
    "category": email.category,
    "isRead": email.is_read,
    "shard": email.shard,
}).to_string();

    // ✅ Print the outgoing body
    println!("📤 Sending email summary payload:\n{}", body);

    let response = client
        .post(api_url)
        .header("Content-Type", "application/json")
        .body(body)
        .send().await?;

    if response.status().is_success() {
        println!("✅ Email saved successfully");
        Ok(())
    } else {
        let status = response.status();
        let error_body = response.text().await.unwrap_or_default();
        Err(format!("❌ Failed to save email. Status: {}, Body: {}", status, error_body).into())
    }
}
pub fn store_new_email(new_message: &Email) -> Result<(), Error> {
    let max_attempts = 5; // Maximum number of retry attempts
    let mut attempts = 0;
    let mut backoff = 100; // Initial backoff time in milliseconds

    while attempts < max_attempts {
        // let connection = &mut establish_db_connection();
        let connection = &mut get_pooled_connection();

        let result = connection.transaction::<_, Error, _>(|conn| {
            // Check if the email already exists
            let existing_email = emails::dsl::emails
                .filter(emails::dsl::id.eq(&new_message.id))
                .first::<Email>(conn)
                .optional()?;

            if existing_email.is_none() {
                // If the email doesn't exist, insert it
                diesel::insert_into(emails::table).values(new_message).execute(conn)?;
            } else {
                // Optionally, log that the email already exists
                println!("Email with id {} already exists, skipping insertion", new_message.id);
            }

            Ok(())
        });

        match result {
            Ok(_) => {
                return Ok(());
            } // Success, exit the loop
            Err(
                diesel::result::Error::DatabaseError(
                    diesel::result::DatabaseErrorKind::Unknown,
                    info,
                ),
            ) if info.message().contains("database is locked") => {
                // If the database is locked, retry after a delay
                attempts += 1;
                println!(
                    "Database is locked, attempt {}/{}. Inside store_new_email  Retrying in {}ms...",
                    attempts,
                    max_attempts,
                    backoff
                );
                thread::sleep(Duration::from_millis(backoff)); // Wait before retrying
                backoff *= 2; // Exponential backoff
            }
            Err(e) => {
                return Err(e);
            } // For any other error, return immediately
        }
    }

    Err(
        diesel::result::Error::DatabaseError(
            diesel::result::DatabaseErrorKind::Unknown,
            Box::new("Max retry attempts reached, database is locked.".to_string())
        )
    )
}

pub fn delete_emails(email_id: String) -> Result<(), Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete the email from the emails table
        diesel
            ::delete(dsl::emails.filter(crate::schema::emails::dsl::id.eq(email_id)))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error deleting messages: {}", err);
                err
            })?;

        Ok(())
    })
}

pub fn delete_all_emails() -> Result<usize, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete all emails from the emails table
        let deleted_rows = diesel
            ::delete(dsl::emails)
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error deleting all emails: {}", err);
                err
            })?;

        Ok(deleted_rows)
    })
}

pub fn list_emails_with_process_flag() -> Result<Vec<Email>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Initialize the vector that will store all fetched emails
    let mut all_emails: Vec<Email> = Vec::new();

    // Fetch all emails where process_flag is true
    let result = connection.transaction::<_, Error, _>(|conn| {
        dsl::emails
            .filter(dsl::process_flag.eq(true)) // Filter for emails with process_flag = true
            .order_by(dsl::date.desc()) // Sort by date in descending order
            .load::<Email>(conn)
    });

    // Append the result to all_emails vector
    match result {
        Ok(mut emails) => {
            all_emails.append(&mut emails);
        }
        Err(ref e) => {
            println!(
                "Transaction failed for fetching emails with process_flag = true due to: {:?}",
                e
            );
            let retry_result = connection.transaction::<_, Error, _>(|conn| {
                dsl::emails
                    .filter(dsl::process_flag.eq(true)) // Retry fetching emails with process_flag = true
                    .order_by(dsl::date.desc())
                    .load::<Email>(conn)
            });

            if let Ok(mut retry_emails) = retry_result {
                all_emails.append(&mut retry_emails);
            } else {
                println!("Retry failed for fetching emails with process_flag = true");
            }
        }
    }

    // Return the complete list of emails with process_flag = true
    Ok(all_emails)
}

// Advanced functions

// •	Total email count
// •	Unread and starred email counts
// •	Emails by category
// •	Latest email date
// •	Unique senders list
// •	Email count within a date range

pub fn count_total_emails() -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Count all emails in the emails table
        let total_emails = dsl::emails
            .count()
            .get_result::<i64>(conn)
            .map_err(|err| {
                eprintln!("Error counting total emails: {}", err);
                err
            })?;

        Ok(total_emails)
    })
}

pub fn count_unread_emails() -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Count emails where the unread flag is set
        let unread_emails = dsl::emails
            .filter(dsl::is_read.eq(true))
            .count()
            .get_result::<i64>(conn)
            .map_err(|err| {
                eprintln!("Error counting unread emails: {}", err);
                err
            })?;

        Ok(unread_emails)
    })
}
// pub fn count_emails_by_category() -> Result<Vec<(String, i64)>, Error> {
//     let connection = &mut establish_db_connection();

//     connection.transaction::<_, Error, _>(|conn| {
//         // Group emails by category and count each group
//         let email_counts = dsl::emails
//             .select((dsl::category()))
//             .group_by(dsl::category)
//             .load::<(String, i64)>(conn)
//             .map_err(|err| {
//                 eprintln!("Error counting emails by category: {}", err);
//                 err
//             })?;

//         Ok(email_counts)
//     })
// }

pub fn get_unique_senders() -> Result<Vec<Option<String>>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Select distinct sender addresses
        let senders = dsl::emails
            .select(dsl::main_domain)
            .distinct()
            .load::<Option<String>>(conn)
            .map_err(|err| {
                eprintln!("Error fetching unique senders: {}", err);
                err
            })?;

        Ok(senders)
    })
}

// pub fn count_starred_emails() -> Result<i64, Error> {
//     let connection = &mut establish_db_connection();

//     connection.transaction::<_, Error, _>(|conn| {
//         // Count emails where the starred flag is set
//         let starred_emails = dsl::emails
//             .filter(dsl::emails::starred.eq(true))
//             .count()
//             .get_result::<i64>(conn)
//             .map_err(|err| {
//                 eprintln!("Error counting starred emails: {}", err);
//                 err
//             })?;

//         Ok(starred_emails)
//     })
// }

pub fn count_emails_by_date_range(
    start_date: NaiveDateTime,
    end_date: NaiveDateTime
) -> Result<i64, Error> {
    let mut conn = get_pooled_connection(); // 🔁 same pattern as before

    conn.transaction::<_, _, _>(|conn| {
        let emails_in_range = dsl::emails
            .filter(dsl::date.between(start_date, end_date))
            .count()
            .get_result::<i64>(conn)?;

        Ok(emails_in_range)
    })
    // let connection = &mut establish_db_connection();

    // connection.transaction::<_, Error, _>(|conn| {
    //     // Count emails within the specified date range
    //     let emails_in_range = dsl::emails
    //         .filter(dsl::date.between(start_date, end_date))
    //         .count()
    //         .get_result::<i64>(conn)
    //         .map_err(|err| {
    //             eprintln!("Error counting emails by date range: {}", err);
    //             err
    //         })?;

    //     Ok(emails_in_range)
    // })
}

// pub fn get_today_categories() -> Result<Vec<String>, diesel::result::Error> {
//     let mut conn = get_pooled_connection();
//     let fixed_categories = vec![
//         "needs_reply",
//         "waiting_response",
//         "fyi_read_later",
//         "delegated_handled",
//         "calendar_scheduling",
//         "clients_vips",
//         "ads_newsletters",
//     ];

//     let today: NaiveDate = chrono::Local::now().date_naive();

//     // Fetch distinct storage_location where not null and date is today
//     let categories: Vec<String> = dsl::emails
//         .select(dsl::storage_location)
//         .filter(dsl::storage_location.is_not_null())
//         // .filter(date(dsl::date).eq(today))
//         .distinct()
//         .load::<Option<String>>(&mut conn)?
//         .into_iter()
//         .filter_map(|opt| opt) // remove nulls
//         .collect::<HashSet<_>>() // deduplicate
//         .into_iter()
//         .collect();
//     println!("📦 Today’s non-null storage categories: {:?}", categories);

//     Ok(categories)
// }

pub fn get_today_categories() -> Result<Vec<EmailCategory>, diesel::result::Error> {
    let mut conn = get_pooled_connection();

    let fixed_categories = vec![
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters"
    ];

    // Fetch distinct storage_location where not null
    let category_ids: Vec<String> = dsl::emails
        .select(dsl::storage_location)
        .filter(dsl::storage_location.is_not_null())
        .distinct()
        .load::<Option<String>>(&mut conn)?
        .into_iter()
        .filter_map(|opt| opt) // remove nulls
        .collect::<HashSet<_>>() // deduplicate
        .into_iter()
        .collect();

    println!("\u{1F4E6} Today’s non-null storage categories: {:?}", category_ids);

    // Map to detailed EmailCategory list
    let results: Vec<EmailCategory> = category_ids
        .into_iter()
        .filter_map(|category_id| {
            if fixed_categories.contains(&category_id.as_str()) {
                Some(EmailCategory {
                    id: Uuid::new_v4().to_string(),
                    category_id: category_id.clone(),
                    child_full_domains: Some(category_id.clone()),
                    name: Some(humanize_category_name(&category_id)),
                    sender_company: Some(humanize_category_name(&category_id)),
                    sender_domain: Some(humanize_category_name(&category_id)),
                    avatar_link: Some(humanize_category_name(&category_id)),
                    created_at: Utc::now().naive_utc(),
                    tags: Some(humanize_category_name(&category_id)),
                    latest_email_at: Some(Utc::now().naive_utc()),
                    description: Some(generate_description(&category_id)),
                    priority: Some(get_priority(&category_id)),
                    color: Some(get_color(&category_id)),
                    unread_count: Some(10),
                    last_accessed_at: None,
                    is_archived: Some(false),
                    custom_data: Some(humanize_category_name(&category_id)),
                    parent_category_id: Some(humanize_category_name(&category_id)),
                    class_category_id: Some(String::new()),
                    visibility: Some("visible".to_string()),
                    is_synced: Some(true),
                })
            } else {
                None
            }
        })
        .collect();

    Ok(results)
}

fn humanize_category_name(id: &str) -> String {
    (
        match id {
            "needs_reply" => "Needs Reply",
            "waiting_response" => "Waiting for Response",
            "fyi_read_later" => "FYI Read Later",
            "delegated_handled" => "Delegated Handled",
            "calendar_scheduling" => "Calendar Scheduling",
            "clients_vips" => "Clients VIPs",
            "ads_newsletters" => "Ads Newsletters",
            _ => id,
        }
    ).to_string()
}

fn generate_description(id: &str) -> String {
    (
        match id {
            "needs_reply" => "Emails that need your immediate reply",
            "waiting_response" => "Emails awaiting reply from others",
            "fyi_read_later" => "Informational emails you might read later",
            "delegated_handled" => "Emails handled or delegated to others",
            "calendar_scheduling" => "Meeting invites and scheduling emails",
            "clients_vips" => "Important clients, VIPs, and partners emails",
            "ads_newsletters" => "Promotions, ads, and newsletters",
            _ => "General category",
        }
    ).to_string()
}

fn get_priority(id: &str) -> i32 {
    match id {
        "clients_vips" => 0,
        "needs_reply" => 1,
        "waiting_response" => 2,
        "calendar_scheduling" => 3,
        "fyi_read_later" => 4,
        "delegated_handled" => 5,
        "ads_newsletters" => 6,
        _ => 9,
    }
}

fn get_color(id: &str) -> String {
    (
        match id {
            "needs_reply" => "#FF4C4C",
            "waiting_response" => "#FFA500",
            "fyi_read_later" => "#5B9BD5",
            "delegated_handled" => "#7FBA00",
            "calendar_scheduling" => "#FFB6C1",
            "clients_vips" => "#FFD700",
            "ads_newsletters" => "#CCCCCC",
            _ => "#DDDDDD",
        }
    ).to_string()
}

pub async fn get_fresh_access_token(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>
) -> Result<String, String> {
    check_and_refresh_token(&app_data).await.map_err(|e|
        format!("Failed to refresh token: {}", e)
    )?;

    // Step 1: Acquire read lock to retrieve necessary data
    let access_token = {
        let app_data_arc = app_data.read().await; // Acquire read lock for `app_data`
        let user_data = app_data_arc.user_data.read().await; // Acquire read lock for `user_data`

        user_data.access_token.as_ref().map(|t| t.secret().clone())
    }; // Read locks are released here

    // Step 2: Use or log the retrieved data as needed
    println!("Refreshed Access Token: {}", access_token.clone().unwrap());

    // println!("this inside js2rs \n {:#?}", user_data_json_data);

    return Ok(access_token.unwrap());
}
