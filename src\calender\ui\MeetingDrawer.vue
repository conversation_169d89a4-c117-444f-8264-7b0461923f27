<template>
  <div
    :class="{ 'w-1/3': props.meeting, 'w-0': !props.meeting }"
    class="h-full flex flex-col transition-all duration-300 overflow-hidden border-l-2 border-l-secondary-600"
  >
    <div class="flex justify-end w-full h-6">
      <button class="hover:text-red-600" @click="() => meetingsStore.setSelectedMeeting(null)">
        <XMarkIcon class="size-5" />
      </button>
    </div>
    <div v-if="isLoading" class="flex flex-col justify-center items-center h-full">
      <div class="text-6xl flex justify-center items-center flex-col gap-2 text-secondary-700">
        <Icon icon="line-md:loading-twotone-loop" />
        <div class="text-xs">Loading Details</div>
      </div>
    </div>
    <div v-else class="flex flex-col px-1 gap-2">
      <div class="rounded-md overflow-hidden">
        <video :src="meeting?.signedRecording" controls></video>
      </div>
      <div class="flex flex-col gap-1">
        <div class="font-semibold text-secondary-500 text-xs">summary</div>
        <div class="overflow-y-auto max-h-60 custom-scrollbar pl-2 border-l-2 py-1 border-l-secondary-600">
          <p class="text-sm text-justify">{{ meeting?.summarizationDetails.summary }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from "vue";
import { MeetingsRecordings, MeetingsRecordingsDetails } from "../../models/meetings-modal";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { useMeetingsStore } from "../../stores/meetingsStore";
import { MeetingBotService } from "../../services/meeting-bot-service";
import { Icon } from "@iconify/vue/dist/iconify.js";

const meetingsStore = useMeetingsStore();

const props = defineProps({
  meeting: {
    type: Object,
    required: false,
  },
});

const meeting = ref<MeetingsRecordingsDetails | null>(null);
const isLoading = ref(true);

watchEffect(async () => {
  meeting.value = null;
  if (props.meeting?.id) {
    isLoading.value = true;
    meeting.value = await MeetingBotService.getMeetingDetails(props.meeting.id);
    //  console.log("meeting", meeting.value);
    isLoading.value = false;
  }
});
</script>

<style scoped></style>
