use crate::{
    models::email_log::{EmailLog, NewEmailLog},
    services::email_log_service,
};
use chrono::NaiveDateTime;

#[tauri::command]
pub fn add_email_snooze(email_id: String, snoozed_until: String) -> Result<EmailLog, String> {
    let snooze_time = NaiveDateTime::parse_from_str(&snoozed_until, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid datetime: {}", e))?;

    let new_log = NewEmailLog {
        email_id,
        snoozed_until: snooze_time,
    };

    email_log_service::insert_snooze(new_log)
}

#[tauri::command]
pub fn get_active_snoozed_emails() -> Vec<EmailLog> {
    email_log_service::get_active_snoozed()
}

#[tauri::command]
pub fn resurface_due_emails() -> Result<usize, String> {
    email_log_service::resurface_due_emails()
}

#[tauri::command]
pub fn get_all_email_logs() -> Vec<EmailLog> {
    email_log_service::get_all()
}

#[tauri::command]
pub fn get_email_log_by_id(id: String) -> Option<EmailLog> {
    email_log_service::get_by_id(id)
}

#[tauri::command]
pub fn delete_email_log(id: String) -> Result<(), String> {
    email_log_service::delete(id)
}