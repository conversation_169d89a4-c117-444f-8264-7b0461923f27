import axios from "axios";
import { WeatherForecast } from "../models/weather-models";



async function getWeather(q: string, days: number = 3) {
  try {
    //  console.log("Q", q, import.meta.env.VITE_WEATHER_API_KEY);
    const options = {
      method: 'GET',
      url: `https://${import.meta.env.VITE_WEATHER_API_HOST}/forecast.json`,
      params: { q, days: days },
      headers: {
        'x-rapidapi-key': import.meta.env.VITE_WEATHER_API_KEY,
        'x-rapidapi-host': import.meta.env.VITE_WEATHER_API_HOST
      }
    };
    return await axios.request<WeatherForecast>(options);
  } catch (error) {
    console.error(error);
  }
}




export const WeatherService = {
  getWeather
}
