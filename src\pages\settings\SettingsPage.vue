<template>
  <div class="w-screen h-screen bg-primary p-0 m-0 flex flex-col gap-3">
    <TitleBar />
    <!-- Page Title -->
    <div class="w-full h-auto px-6">
      <AccountSection />
    </div>
    <div class="w-full h-auto px-6">
      <AboutSection />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import TitleBar from "../../components/TitleBar.vue";
import { invoke } from "@tauri-apps/api/core";
import { Settings } from "../../types";
import { useRouter } from "vue-router";
import { useCurrentUserStore } from "../../stores/currentUser";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import auth from "../../commands/auth";
import AccountSection from "./sections/AccountSection.vue";
import AboutSection from "./sections/AboutSection.vue";
const appWindow = getCurrentWebviewWindow();

const loading = ref(false);
const me = ref({
  email: "",
  name: "",
  picture: "",
  unreadEmailCount: 0,
  totalEmailCount: 0,
  emailCategories: 0,
});
const title = ref("Tari + oauth example");
const router = useRouter();
const editOpen = ref(false);

const currentUserStore = useCurrentUserStore();

// Initialize settings object
const settings = ref<Settings>({
  open_ai_secret: "",
  open_ai_model: "",
  // smtp_server: "",
  // smtp_port: 587,
  // smtp_username: "",
  // smtp_password: "",
  // notification_email: "",
  // notification_sms: "",
  // theme: "system", // Default theme
});

const saving = ref(false);

// OpenAI model options
const openAIModelOptions = ref([
  "gpt-3.5-turbo",
  "gpt-3.5-turbo-0613",
  "gpt-3.5-turbo-16k-0613",
  "gpt-4",
  "gpt-4-1106-preview",
]);

onMounted(async () => {
  await currentUserStore.init();
  // Load the settings on mount
  // settings.value = await invoke("get_settings");
});

async function save() {
  await invoke("set_settings", { settings: settings.value });
  setToSaving();
}

function setToSaving() {
  saving.value = true;

  setTimeout(() => {
    saving.value = false;
  }, 1000);
}
</script>

<style scoped>
/* Custom styling for the form and save button */
/* Optional: Better scrollbar */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
</style>
