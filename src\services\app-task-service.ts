import { invoke } from "@tauri-apps/api/core";
import { Draft, Task, TasksStat } from "../models/task-model";



async function listTasks(filter: "completed" | "in_queue" | undefined): Promise<Task[]> {
  let tasks = await invoke<Task[]>("list_tasks", { filter });
  return tasks
}


async function getTaskDraft(taskId: number): Promise<Draft | null> {
  let draft = await invoke<Draft | null>("task_draft", { taskId });
  return draft
}

async function getThreadDrafts(threadId: string): Promise<Draft[]> {
  let draft = await invoke<Draft[]>("thread_drafts", { threadId });
  return draft
}

async function getDrafts(): Promise<Draft[]> {
  let draft = await invoke<Draft[]>("waiting_drafts", {});
  return draft
}

async function getTasksStat(): Promise<TasksStat> {
  let draft = await invoke<TasksStat>("get_task_stats", {});
  return draft
}

export const AppTasksService = {
  listTasks,
  getTaskDraft,
  getThreadDrafts,
  getTasksStat,
  getDrafts
}
