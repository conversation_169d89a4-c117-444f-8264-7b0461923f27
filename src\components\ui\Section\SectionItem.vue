<template>
  <div class="w-full h-9 flex justify-center items-center px-2x">
    <div
      class="w-full border-t h-full flex items-center px-2"
      :class="{
        'border-t-transparent': props.position == 'first',
        'border-transparent': props.position == 'none',
        ' border-t-secondary-400': !props.position,
      }"
    >
      <div class="size-full">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  position: {
    type: String,
    default: "none",
  },
});
</script>

<style scoped></style>
