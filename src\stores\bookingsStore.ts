// stores/apiStore.js
import { defineStore } from 'pinia';
import { calBookingsService } from '../services/bookings-cal-service';
import { useCurrentUserStore } from './currentSession';
import { Booking } from '../models/booking-model';

interface State {
  bookings: Booking[],
  todayBookings: Booking[],
  isLoading: boolean,
  error: string | null
}



export const useBookingStore = defineStore('bookingsStore', {
  state: (): State => ({
    bookings: [],
    todayBookings: [],

    isLoading: false,
    error: null,
  }),
  actions: {
    async fetchBookings() {
      this.isLoading = true;
      try {
        const session = useCurrentUserStore();
        if (session.calInfo?.id) {
          this.bookings = await calBookingsService.getUserBookings(session.calInfo?.id) ?? [];
          //  console.log("@store@", this.bookings);
          this.error = null;
        }
      } catch (err: any) {
        this.error = err.message;
      } finally {
        this.isLoading = false;
      }
    },
    async fetchTodayBookings() {
      this.isLoading = true;
      try {
        const session = useCurrentUserStore();
        if (session.calInfo?.id) {
          //  console.log("Calinfo Id", session.calInfo?.id);
          const result = await calBookingsService.getTodayBookings(session.calInfo?.id)
          //  console.log("[fetchTodayBookings]: result:", result);
          this.todayBookings = result ?? [];
          this.error = null;
        }
      } catch (err: any) {
        this.error = err.message;
      } finally {
        this.isLoading = false;
      }
    }
  },
});
