<template>
  <div class="relative max-h-80 overflow-hidden">
    <select
      class="w-fitx py-1 max-h-80 overflow-y-auto text-dark-900 rounded-md bg-secondary-300 custom-scrollbar"
      v-model="selectedTime"
      placeholder="Chose Time"
    >
      <option v-for="time of times" :key="time.label" :value="time.value">
        {{ time.label }}
      </option>
    </select>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, watchEffect } from "vue";
import { generateTimeIntervals, normalizeTime, TimeInterval, timeToISOString } from "../../utils/availability-utils";

const props = defineProps({
  value: {
    type: String,
  },
});

const times = ref<TimeInterval[]>(generateTimeIntervals());
const selectedTime = ref(props.value && props.value != "none" ? timeToISOString(props.value) : undefined);

const emit = defineEmits<{
  (e: "select", value: string): void;
}>();

watch(selectedTime, () => {
  if (selectedTime.value) emit("select", selectedTime.value);
});

onMounted(() => {
  //  console.log("Times Intervals =>", times);
  //  console.log("SelectedTime =>", selectedTime);
});
</script>

<style scoped></style>
