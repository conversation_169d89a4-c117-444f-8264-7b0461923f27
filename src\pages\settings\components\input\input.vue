<template>
  <div class="flex-1">
    <div v-if="!props.editing" class="text-sm font-semibold flex-1 text-right">{{ props.value }}</div>
    <div v-else>
      <input
        v-if="props.type === 'text'"
        type="text"
        class="h-6 bg-primary-300/20 border-none rounded-md p-1 drop-shadow-sm w-full"
        :value="props.value"
        @input="(e: any) => emit('onUpdate', props.attr || 'text', e.target.value)"
      />
      <TimeZoneSelect
        v-else-if="props.type === 'timezone'"
        :value="props.value"
        @select="(value: string) => emit('onUpdate', 'timeZone', value)"
      />

      <div v-else-if="props.type === 'weekstart'" class="flex items-center justify-between">
        <button
          class="siz-7 flex justify-center items-center bg-primary-400"
          @click="updateWeekStart((weekStartIndex - 1 + 7) % 7)"
        >
          <ChevronLeftIcon class="size-5" />
        </button>
        <div class="text-sm font-semibold text-right">{{ days[weekStartIndex] }}</div>
        <button
          class="siz-7 flex justify-center items-center bg-primary-400"
          @click="updateWeekStart((weekStartIndex + 1) % 7)"
        >
          <ChevronRightIcon class="size-5" />
        </button>
      </div>
      <div v-else-if="props.type === 'locale'" class="flex items-center justify-between">
        <button
          class="siz-7 flex justify-center items-center bg-primary-400"
          @click="updateLocale((localeIndex - 1 + locales.length) % locales.length)"
        >
          <ChevronLeftIcon class="size-5" />
        </button>
        <div class="text-sm font-semibold text-right">{{ locales[localeIndex].label }}</div>
        <button
          class="siz-7 flex justify-center items-center bg-primary-400"
          @click="updateLocale((localeIndex + 1) % locales.length)"
        >
          <ChevronRightIcon class="size-5" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/vue/20/solid";
import TimeZoneSelect from "./TimeZoneSelect.vue";
import { ref } from "vue";
const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const locales = [
  { label: "English", value: "en" },
  { label: "Spanish", value: "es" },
  { label: "French", value: "fr" },
  { label: "Arabic", value: "ar  " },
];

const props = defineProps({
  value: String,
  type: {
    type: String,
    default: "text",
  },
  attr: String,
  editing: Boolean,
});

const emit = defineEmits<{
  (e: "onUpdate", attr: string, value: string): void;
}>();

const weekStartIndex = ref(Array.isArray(props.value) && props.value ? days.indexOf(props.value) : 0);
const localeIndex = ref(props.attr === "locale" ? locales.findIndex((l) => l.value === props.value) : 0);

const updateWeekStart = (newIndex: number) => {
  weekStartIndex.value = newIndex;
  emit("onUpdate", "weekStart", days[newIndex]);
};

const updateLocale = (newIndex: number) => {
  localeIndex.value = newIndex;
  emit("onUpdate", "locale", locales[newIndex].value);
};
</script>

<style scoped></style>
