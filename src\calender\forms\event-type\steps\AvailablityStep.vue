<template>
  <div class="size-full flex flex-col gap-4">
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div class="flex flex-col gap-1">
        <label for="">Availablity</label>
        <select class="bg-transparent rounded-md" placeholder="Select Availability" v-model="selectedScheduleId">
          <option v-for="s of schedules" :value="s.id">{{ s.name }}</option>
        </select>
      </div>
    </div>
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div
        :key="selectedScheduleId"
        class="w-full min-h-12 drop-shadow px-2 rounded-md flex gap-8 justify-start items-start py-2"
        v-for="(value, index) of DAYS"
      >
        <div class="w-40" :class="dayActive(index) ? '' : 'line-through'">{{ value }}</div>
        <div>
          <div v-if="dayActive(index)" v-for="(av, i) of availability.days[index]">
            {{ av.startTime }} - {{ av.endTime }}
          </div>
          <div v-else>Unavailable</div>
        </div>
      </div>
      <div class="pt-4 border-t border-primary-300 flex items-center text-sm gap-2">
        <GlobeAltIcon class="size-5 text-primary-700" />
        <div>{{ selectedSchedule?.timeZone }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useCurrentUserStore } from "../../../../stores/currentUser";
import { CalSchedule } from "../../../../models/schedule-model";
import { calSchedulesService } from "../../../../services/schedules-cal-service";
import { AvailGroups, groupAvailability } from "../../../../utils/availability-utils";
import { DAYS } from "../../../../models/availability-model";
import { GlobeAltIcon } from "@heroicons/vue/24/outline";
const props = defineProps({
  selectedId: {
    type: Number,
  },
});
const session = useCurrentUserStore();
const schedules = ref<CalSchedule[]>([]);
const selectedScheduleId = ref<number | undefined>(props.selectedId ?? session.calInfo?.defaultScheduleId ?? undefined);
const selectedSchedule = ref<CalSchedule | undefined>();
const availability = computed<AvailGroups>(() => {
  selectedSchedule.value = schedules.value.find((s) => s.id === selectedScheduleId.value);
  //  console.log("Selected Schedule =>", selectedScheduleId.value, selectedSchedule.value);
  if (selectedSchedule.value) return groupAvailability(selectedSchedule.value.availability);
  return { days: {}, dates: {} };
});

async function getSchedules() {
  if (session.calInfo?.id) schedules.value = (await calSchedulesService.getUserSchedules(session.calInfo?.id)) ?? [];
}
function dayActive(index: number) {
  const active = Object.keys(availability.value.days ?? []).includes(index.toString());
  // //  console.log(`${index} Day ${DAYS[index]} is Active ? ${active}`);
  return active;
}

const emit = defineEmits<{
  (e: "change", value: number): void;
}>();

watch(selectedScheduleId, (value) => {
  emit("change", value ?? session.calInfo?.defaultScheduleId ?? 0);
});

onMounted(async () => {
  //  console.log("############ >", session.calInfo);

  await getSchedules();
});
</script>

<style scoped></style>
