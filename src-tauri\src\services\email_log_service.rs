use diesel::{
    query_dsl::methods::{FilterDsl, OrderDsl},
    BoolExpressionMethods,
    ExpressionMethods,
    RunQueryDsl,
};
use chrono::NaiveDateTime;

use crate::{
    db::get_pooled_connection,
    models::email_log::{EmailLog, NewEmailLog},
    schema::email_logs::dsl::*,
};

/// Insert new snooze log
pub fn insert_snooze(new_log: NewEmailLog) -> Result<EmailLog, String> {
    let conn = &mut get_pooled_connection();

    let result = diesel::insert_into(email_logs)
        .values(&new_log)
        .returning((id, email_id, snoozed_until, created_at, resurfaced))
        .get_result::<EmailLog>(conn)
        .map_err(|e| e.to_string())?;

    Ok(result)
}

/// Fetch all logs
pub fn get_all() -> Vec<EmailLog> {
    let conn = &mut get_pooled_connection();

    email_logs
        .order(created_at.desc())
        .load::<EmailLog>(conn)
        .expect("Failed to load email logs")
}

/// Fetch by ID
pub fn get_by_id(log_id: String) -> Option<EmailLog> {
    let conn = &mut get_pooled_connection();

    email_logs
        .filter(id.eq(log_id))
        .first::<EmailLog>(conn)
        .ok()
}

/// Delete a snooze log by ID
pub fn delete(log_id: String) -> Result<(), String> {
    let conn = &mut get_pooled_connection();

    diesel::delete(email_logs.filter(id.eq(log_id)))
        .execute(conn)
        .map_err(|e| e.to_string())?;

    Ok(())
}

/// Get active snoozed emails (not yet resurfaced)
pub fn get_active_snoozed() -> Vec<EmailLog> {
    let conn = &mut get_pooled_connection();

    email_logs
        .filter(resurfaced.eq(false))
        .filter(snoozed_until.gt(chrono::Utc::now().naive_utc()))
        .order(snoozed_until.asc())
        .load::<EmailLog>(conn)
        .unwrap_or_default()
}

/// Resurface due emails — update resurfaced = true
pub fn resurface_due_emails() -> Result<usize, String> {
    let conn = &mut get_pooled_connection();

    let updated = diesel::update(email_logs
        .filter(resurfaced.eq(false))
        .filter(snoozed_until.le(chrono::Utc::now().naive_utc())))
        .set(resurfaced.eq(true))
        .execute(conn)
        .map_err(|e| e.to_string())?;

    Ok(updated)
}