import { invoke } from "@tauri-apps/api/core";
import { defineStore } from "pinia";
import { User, EmailContext, Session, EmailCategory, PhoneCallContext } from "../types.d";
import { calUsersService } from "../services/user-cal-service";
import { CalUser } from "../models/user";

interface State {
  currentSession?: Session;
}

export const useCurrentSessionStore = defineStore("currentSession", {
  state: (): State => ({
    currentSession: undefined,
  }),
  actions: {
    async init() {
      const id = localStorage.getItem("selectedSession");

      if (!id) {
        return;
      }

      this.currentSession = await invoke("get_session", { id });
    },

    async selectSession(id: string) {
      this.currentSession = await invoke("get_session", { id });
      localStorage.setItem("selectedSession", id);
    },
  },
});

interface State {
  currentEmailCategory?: EmailCategory;
  categoryPages: Record<string, number>; // 👈 Maps categoryId -> page number

}

export const useCurrentEmailCategoryStore = defineStore("currentEmailCategory", {
  state: (): State => ({
    currentEmailCategory: undefined,
    categoryPages: {}, // ⬅️ categoryId: pageNumber

  }),
  actions: {
    async init() {
      const id = localStorage.getItem("selectedEmailCategory");

      if (!id) {
        return;
      }

      this.currentEmailCategory = await invoke("get_email_category", { categoryId: id });
      if (!(id in this.categoryPages)) {
        this.categoryPages[id] = 1;
      }
    },

    async selectEmailCategory(id: string, email_category?: EmailCategory) {
      //  console.log("Switching to category:", id);
      const fixed_categories = [
        "needs_reply", // needs more information or action
        "waiting_response", // waiting for response from someone
        "fyi_read_later", // summarized email from bosss or manager
        "delegated_handled", // handled by someone in vip/team table
        "calendar_scheduling", // find_meeting_time
        "clients_vips", // have table for this vip/team table
        "ads_newsletters", // No formational
      ];

      if (fixed_categories.includes(id)) {
        this.currentEmailCategory = email_category;
      } else {
        this.currentEmailCategory = await invoke("get_email_category", { categoryId: id });
      }
      localStorage.setItem("selectedEmailCategory", id);

      // ⬇️ If we’ve never paginated this category, init to page 0
      if (!(id in this.categoryPages)) {
        this.categoryPages[id] = 1;
        //  console.log("Switching to category:", id);

      }
    },

    incrementPageForCurrentCategory() {
      const id = this.currentEmailCategory?.id;
      if (!id) return;

      this.categoryPages[id] = (this.categoryPages[id] || 1) + 1;
    },

    resetPageForCategory(id: string) {
      this.categoryPages[id] = 1;
    },

    getCurrentPage(): number {
      const id = this.currentEmailCategory?.id;
      return id ? this.categoryPages[id] ?? 1 : 1;
    },
    // async selectEmailCategory(id: string) {
    //   //  console.log(id);
    //   this.currentEmailCategory = await invoke("get_email_category", { categoryId: id });
    //   localStorage.setItem("selectedEmailCategory", id);
    // },
  },
});

interface State_ {
  currentUser?: User;
  currentEmailContext?: EmailContext;
  currentPhoneContext?: PhoneCallContext;
  calInfo?: CalUser;
}

export const useCurrentUserStore = defineStore("useCurrentUserStore", {
  state: (): State_ => ({
    currentUser: {
      id: "",
      email: "",
      verified_email: false,
      name: "",
      given_name: "",
      family_name: "",
      picture: "",
      refreshToken: "",
      expires_in: "",
      access_token: "",
      username: "",
      locale: "en-US", // Default locale
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
      login_count: 0,
      role: "user",
      is_active: true,

      // Optional fields with defaults
      notification_preferences: {
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
      },
      social_accounts: {
        google: undefined,
        facebook: undefined,
        twitter: undefined,
      },
      privacy_settings: {
        data_sharing: false,
        targeted_ads: false,
      },
      theme_preference: "light",
      custom_metadata: {},
    } as User,
    currentEmailContext: {
      full_name: "",
      job_title: "",
      organization: "",
      target_audience: "",
      communication_goal: "",
      call_to_action: "",
      tone_preference: "",
      language_style: "",
      key_points: "",
      known_preferences: [],
      personal_sign_off: "",
      email_signature: "",
      work_hours: [],
      availability_note: "",
      email_length_preference: "",
      urgency_level: "",
      email_length: 250
    } as EmailContext,
    currentPhoneContext: {
      fullName: "",
      jobTitle: "",
      organization: "",
      targetAudience: "",
      communicationGoal: "",
      tonePreference: "",
      callToAction: "",
      keyPoints: "",
      languageStyle: "",
      urgencyLevel: "",
      greetingMessage: "",
      adaptiveLearningEnabled: false,
      responseTimePreferences: "",
      defaultEscalationPath: "",
      backupAgentContact: "",
      additionalResources: "",
      supportedLanguages: [],
      sentimentTriggers: [],
      contextualData: [],
      businessOverview: "",
      pricingInfo: "",
      frequentlyAskedQuestions: [],
      servicesOffered: [],
      workHours: [],
      availabilityNote: "",
      contactEmail: "",
      contactPhone: "",
      locationAddress: "",
      preferredResponsePatterns: [],
      personalizationTriggers: [],
      followUpMessage: "",
      dynamicScripts: [],
      escalationContact: "",
    } as PhoneCallContext,
    calInfo: {
      id: 0,
      email: "",
      username: "",
      bio: "",
      createdDate: "",
      role: "",
      defaultScheduleId: 0,
      weekStart: "SUNDAY",
      timeZone: "",
      timeFormat: "",
      locale: "",
      avatar: "",
      password: "",
    } as CalUser,
  }),
  actions: {
    async init() {
      const currentUser = localStorage.getItem("selectedSession");
      if (!currentUser) {
        return;
      }

      try {
        const data: any = await invoke("js2rs", {
          message: "get_stored_user",
        });

        if (!data) {
          console.error("No data received from invoke");
          return;
        }

        let me_data;
        try {
          me_data = JSON.parse(data);
          //  console.log("Parsed data:", me_data);
        } catch (err) {
          console.error("Error parsing data:", err);
          return;
        }

        // Initialize currentUser if undefined
        this.currentUser = this.currentUser ?? ({} as User);
        this.calInfo = this.calInfo ?? ({} as CalUser);

        //  console.log("Current user before update:", this.currentUser);

        // Populate currentUser fields
        this.currentUser.id = me_data.id ?? "";
        this.currentUser.email = me_data.email ?? "";
        this.currentUser.verified_email = me_data.verified_email ?? "";
        this.currentUser.name = me_data.name ?? "";
        this.currentUser.given_name = me_data.given_name ?? "";
        this.currentUser.family_name = me_data.family_name ?? "";
        this.currentUser.picture = me_data.picture ?? "";
        this.currentUser.refreshToken = me_data.refreshToken ?? "";
        this.currentUser.access_token = me_data.access_token ?? "";
        this.currentUser.username = me_data.access_token ?? "";

        if (this.currentUser.email) {
          const calUserapi: any = await calUsersService.getUserByEmail(this.currentUser.email);
          let calUser_data = JSON.parse(calUserapi);

          //  console.log("USER STORE CALUSER => ", calUser_data);
          //  console.log("USER STORE CALUSER => ", this.calInfo);

          if (calUser_data) {
            this.calInfo.id = calUser_data?.id ?? 0;
            this.calInfo.bio = calUser_data?.bio ?? "";
            this.calInfo.createdDate = calUser_data?.createdDate ?? "";
            this.calInfo.role = calUser_data?.role ?? "";
            this.calInfo.defaultScheduleId = calUser_data?.defaultScheduleId ?? 0;
            this.calInfo.email = calUser_data.email ?? "";
            this.calInfo.username = calUser_data.username ?? "";
            this.calInfo.weekStart = calUser_data.weekStart ?? undefined;
            this.calInfo.timeZone = calUser_data.timeZone ?? "";
            this.calInfo.timeFormat = calUser_data.timeFormat ?? undefined;
            this.calInfo.locale = calUser_data.locale ?? "";
            this.calInfo.avatar = calUser_data.avatar ?? "";
            this.calInfo.password = calUser_data.password ?? "";
          }
        }
        //  console.log("Current user after update:", this.currentUser);
      } catch (err) {
        console.error("Error in init function:", err);
      }
    },
    async storeUserInfo(user: User) {
      localStorage.setItem("user", JSON.stringify(user));

      try {
        const data: any = await invoke("js2rs", {
          message: "get_stored_user",
        });

        if (!data) {
          console.error("No data received from invoke");
          return;
        }

        let me_data;
        try {
          me_data = JSON.parse(data);
          //  console.log("Parsed data:", me_data);
        } catch (err) {
          console.error("Error parsing data:", err);
          return;
        }

        // Initialize currentUser if undefined
        this.currentUser = this.currentUser ?? ({} as User);
        this.calInfo = this.calInfo ?? ({} as CalUser);

        //  console.log("Current user before update:", this.currentUser);
        //  console.log("calInfo user before update:", this.calInfo);

        // Populate currentUser fields
        this.currentUser.id = me_data.id ?? "";
        this.currentUser.email = me_data.email ?? "";
        this.currentUser.verified_email = me_data.verified_email ?? "";
        this.currentUser.name = me_data.name ?? "";
        this.currentUser.given_name = me_data.given_name ?? "";
        this.currentUser.family_name = me_data.family_name ?? "";
        this.currentUser.picture = me_data.picture ?? "";
        this.currentUser.refreshToken = me_data.refreshToken ?? "";
        this.currentUser.access_token = me_data.access_token ?? "";
        this.currentUser.username = user.username ?? "thomas thtey theye ";
        if (this.currentUser.email) {
          const calUserapi: any = await calUsersService.getUserByEmail(this.currentUser.email);
          //  console.log("calUserapi STORE CALUSER => ", calUserapi);

          let calUser_data = calUserapi;

          //  console.log("USER STORE CALUSER => ", calUser_data);
          //  console.log("USER STORE CALUSER => ", calUser_data);
          if (calUser_data) {
            this.calInfo.id = calUser_data.id ?? 0;
            this.calInfo.bio = calUser_data.bio ?? "";
            this.calInfo.createdDate = calUser_data.createdDate ?? "";
            this.calInfo.role = calUser_data.role ?? "";
            this.calInfo.defaultScheduleId = calUser_data.defaultScheduleId ?? 0;
            this.calInfo.email = calUser_data.email ?? "";
            this.calInfo.username = calUser_data.username ?? "";
            this.calInfo.weekStart = calUser_data.weekStart ?? undefined;
            this.calInfo.timeZone = calUser_data.timeZone ?? "";
            this.calInfo.timeFormat = calUser_data.timeFormat ?? undefined;
            this.calInfo.locale = calUser_data.locale ?? "";
            this.calInfo.avatar = calUser_data.avatar ?? "";
            this.calInfo.password = calUser_data.password ?? "";
          }
          localStorage.setItem("calInfo", JSON.stringify(calUser_data));
        }

        //  console.log("Current user after update:", this.currentUser);
      } catch (err) {
        console.error("Error in init function:", err);
      }

      const storedPhoneContext = localStorage.getItem("phoneContext");
      if (storedPhoneContext) {
        try {
          this.currentPhoneContext = JSON.parse(storedPhoneContext) as PhoneCallContext;
        } catch (error) {
          console.error("Error parsing stored phone context:", error);
        }
      }

      const storedEmailContext = localStorage.getItem("emailContext");
      if (storedEmailContext) {
        try {
          this.currentEmailContext = JSON.parse(storedEmailContext) as EmailContext;
        } catch (error) {
          console.error("Error parsing stored email context:", error);
        }
      }
    },
    async updateCurrentCalUser() {
      if (this.currentUser && this.currentUser.email) {
        const calUser = await calUsersService.getUserByEmail(this.currentUser.email);
        //  console.log("#### USER UPDATE CALUSER => ", calUser);
        if (calUser) this.calInfo = calUser;
      }
    },
    async storePhoneContextInfo(phoneContext: PhoneCallContext) {
      localStorage.setItem("phoneContext", JSON.stringify(phoneContext));
      this.currentPhoneContext!.fullName = phoneContext.fullName ?? "";
      this.currentPhoneContext!.jobTitle = phoneContext.jobTitle;
      this.currentPhoneContext!.organization = phoneContext.organization;
      this.currentPhoneContext!.targetAudience = phoneContext.targetAudience;
      this.currentPhoneContext!.communicationGoal = phoneContext.communicationGoal;
      this.currentPhoneContext!.tonePreference = phoneContext.tonePreference;
      this.currentPhoneContext!.callToAction = phoneContext.callToAction;
      this.currentPhoneContext!.languageStyle = phoneContext.languageStyle;
      this.currentPhoneContext!.urgencyLevel = phoneContext.urgencyLevel;
      this.currentPhoneContext!.greetingMessage = phoneContext.greetingMessage;
      this.currentPhoneContext!.adaptiveLearningEnabled = phoneContext.adaptiveLearningEnabled;
      this.currentPhoneContext!.responseTimePreferences = phoneContext.responseTimePreferences;
      this.currentPhoneContext!.defaultEscalationPath = phoneContext.defaultEscalationPath;
      this.currentPhoneContext!.backupAgentContact = phoneContext.backupAgentContact;
      this.currentPhoneContext!.additionalResources = phoneContext.additionalResources;
      this.currentPhoneContext!.supportedLanguages = phoneContext.supportedLanguages;
      this.currentPhoneContext!.sentimentTriggers = phoneContext.sentimentTriggers;
      this.currentPhoneContext!.contextualData = phoneContext.contextualData;
      this.currentPhoneContext!.businessOverview = phoneContext.businessOverview;
      this.currentPhoneContext!.pricingInfo = phoneContext.pricingInfo;
      this.currentPhoneContext!.frequentlyAskedQuestions = phoneContext.frequentlyAskedQuestions;
      this.currentPhoneContext!.servicesOffered = phoneContext.servicesOffered;
      this.currentPhoneContext!.workHours = phoneContext.workHours;
      this.currentPhoneContext!.availabilityNote = phoneContext.availabilityNote;
      this.currentPhoneContext!.contactEmail = phoneContext.contactEmail;
      this.currentPhoneContext!.contactPhone = phoneContext.contactPhone;
      this.currentPhoneContext!.locationAddress = phoneContext.locationAddress;

      // Missing fields added
      this.currentPhoneContext!.preferredResponsePatterns = phoneContext.preferredResponsePatterns ?? [];
      this.currentPhoneContext!.personalizationTriggers = phoneContext.personalizationTriggers ?? [];
      this.currentPhoneContext!.keyPoints = phoneContext.keyPoints ?? "";
      this.currentPhoneContext!.followUpMessage = phoneContext.followUpMessage ?? "";
      this.currentPhoneContext!.dynamicScripts = phoneContext.dynamicScripts ?? [];
      this.currentPhoneContext!.escalationContact = phoneContext.escalationContact ?? "";
    },
    async storeEmailContextInfo(emailContext: EmailContext) {
      //  console.log("storing email context:", emailContext);

      localStorage.setItem("emailContext", JSON.stringify(emailContext));
      //  console.log("after storig email context:", emailContext);

      this.currentEmailContext!.full_name = emailContext.full_name ?? "";
      this.currentEmailContext!.job_title = emailContext.job_title ?? "";
      this.currentEmailContext!.organization = emailContext.organization ?? "";
      this.currentEmailContext!.target_audience = emailContext.target_audience ?? "";
      this.currentEmailContext!.communication_goal = emailContext.communication_goal ?? "";
      this.currentEmailContext!.call_to_action = emailContext.call_to_action ?? "";
      this.currentEmailContext!.tone_preference = emailContext.tone_preference ?? "";
      this.currentEmailContext!.language_style = emailContext.language_style ?? "";
      this.currentEmailContext!.key_points = emailContext.key_points ?? "";
      this.currentEmailContext!.known_preferences = emailContext.known_preferences ?? [];
      this.currentEmailContext!.personal_sign_off = emailContext.personal_sign_off ?? "";
      this.currentEmailContext!.email_signature = emailContext.email_signature ?? "";
      this.currentEmailContext!.work_hours = emailContext.work_hours ?? [];
      this.currentEmailContext!.availability_note = emailContext.availability_note ?? "";
      this.currentEmailContext!.email_length_preference = emailContext.email_length_preference ?? "";
      this.currentEmailContext!.urgency_level = emailContext.urgency_level ?? "";
    },
    async loadPhoneContextInfo() {
      const storedPhoneContext = localStorage.getItem("phoneContext");
      if (storedPhoneContext) {
        try {
          this.currentPhoneContext = JSON.parse(storedPhoneContext) as PhoneCallContext;
        } catch (error) {
          console.error("Error parsing stored phone context:", error);
        }
      }
    },

    async loadEmailContextInfo() {
      const storedEmailContext = localStorage.getItem("emailContext");
      if (storedEmailContext) {
        try {
          this.currentEmailContext = JSON.parse(storedEmailContext) as EmailContext;
        } catch (error) {
          console.error("Error parsing stored email context:", error);
        }
      }
    },
    async removeLocalStorageKeys() {
      localStorage.removeItem("emailContext");
      localStorage.removeItem("phoneContext");
      localStorage.removeItem("user");
    },
  },
});
