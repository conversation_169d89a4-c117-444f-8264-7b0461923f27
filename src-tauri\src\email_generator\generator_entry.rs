/*
This Rust code automates the tasks of an email copywriter using AI.
The automation process includes the following steps:
1. Content Creation: The structure of emails is generated, including the subject line, introduction, body, and call-to-action (CTA).
   AI models like GPT-2 are used to generate each section based on user-provided input and key points.
2. Personalization: The tone, language style, and content of the email are personalized based on user preferences.
   The AI model adapts to the recipient's preferences, creating a unique, engaging email for each individual.
3. Persuasion and Engagement Techniques: AI applies persuasive writing techniques such as urgency and social proof to enhance engagement
   and increase open rates and conversions.
4. A/B Testing and Optimization: AI generates multiple versions of the same email to optimize performance by testing different variations
   and analyzing performance data.
5. Subject Line Generation: The AI generates relevant and compelling subject lines based on the email's key points, improving engagement.
6. Automated Email Segmentation: Based on user segments (new customers vs. returning customers), AI adjusts email content dynamically.
7. Workflow Efficiency: The code integrates task scheduling and automates the process of email generation and sending, enhancing workflow efficiency.

The code leverages Rust for async tasks and text generation models (like GPT-2) via the `rust-bert` library.
*/

use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use crate::models::user_perference::EmailContext;
use regex::Regex;
use serde::de::DeserializeOwned;
// use rust_bert::pipelines::common::ModelType;
// use rust_bert::pipelines::text_generation::{TextGenerationConfig, TextGenerationModel};
use reqwest::Client;
use serde::{ Deserialize, Serialize };
use serde_json::json;
use serde_json::Value;
use std::error::Error;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::task;
use tracing::{ error, info, warn };

/*
This Rust code automates the tasks of an email copywriter using AI, with enhanced personalization based on the EmailContext struct.
It includes the following features:
1. **Content Creation**: Generates subject lines, introductions, body, and CTA based on user input.
2. **Personalization**: Adjusts tone, language style, and content using the `EmailContext` struct.
3. **Persuasion Techniques**: Adds urgency, social proof, or other techniques to encourage replies.
4. **A/B Testing**: Generates variations of emails for testing different formats.
5. **Segmentation**: Customizes emails based on user segments (e.g., new vs. returning customers).
*/

fn sanitize_json(json_str: &str) -> String {
    let re = Regex::new(r#""[^"\\]*(?:\\.[^"\\]*)*""#).unwrap();
    let mut result = json_str.to_string();

    for mat in re.find_iter(json_str) {
        let range = mat.range();
        let matched = &json_str[range.clone()];
        let sanitized = matched.replace('\n', "\\n");
        result.replace_range(range, &sanitized);
    }

    result
}

pub fn sanitize_and_parse<T: DeserializeOwned>(input: &str) -> Result<T, String> {
    // Step 1: Trim and check empty
    let trimmed = input.trim();
    if trimmed.is_empty() {
        return Err("Empty string,can't parse it as json !".into());
    }

    // Step 2: Remove control characters (except whitespace)
    let cleaned: String = trimmed
        .chars()
        .filter(|c| (!c.is_control() || c.is_whitespace()))
        .collect();

    // Step 3: Check size limit
    // if cleaned.len() > max_size {
    //     return Err(SanitizeError::JsonTooLarge);
    // }
    println!("string before parsing => {:?}", cleaned);
    // Step 4: Parse JSON
    let value: T = serde_json::from_str(&cleaned).unwrap();
    Ok(value)
}

pub async fn email_generator_function_chatgpt(
    key_points: &str,
    tone: &str,
    email_context: &EmailContext,
    enable_context: bool
) -> Result<String, Box<dyn Error>> {
    let client = Client::new();
    let api_key =
        "********************************************************************************************************************************************************************"; //Oway Operator call

    // Construct the prompt with email context details
    let prompt = if enable_context {
        construct_email_prompt_chatgpt(email_context, key_points, tone)
    } else {
        construct_email_prompt_chatgpt_without_context(email_context, key_points, tone)
    };
    println!("Prompt => {}", prompt);
    // Call the OpenAI API to generate the email
    let response = client
        .post("https://api.openai.com/v1/completions")
        .header("Authorization", format!("Bearer {}", api_key))
        .json(
            &json!({
            "model": "gpt-3.5-turbo-instruct",//"text-davinci-003",  // Use GPT-3 model
            "prompt": prompt,
            "temperature": 0.7,           // Control randomness
            "max_tokens": 1000,            // Set reasonable max length for emails
            "top_p": 0.9,                 // Use nucleus sampling for coherence
            "frequency_penalty": 0.1,     // Reduce repetition
            "presence_penalty": 0.3       // Encourage diverse language
        })
        )
        .send().await?;

    // Parse the response to get the generated email content
    let response_json: serde_json::Value = response.json().await?;
    // println!("Response => {:?}", response_json);
    let email_body = response_json["choices"][0]["text"]
        .as_str()
        .unwrap_or("Failed to generate email")
        .to_string();

    println!("Generated Email Body: {}", email_body);
    Ok(sanitize_json(&email_body))
}

// fn construct_email_prompt_chatgpt(
//     email_context: &EmailContext,
//     key_points: &str,
//     tone: &str
// ) -> String {
//     format!(
//         "Compose a {tone} email with the following context:\n\
//         - Recipient Target Audience: {target_audience}\n\
//         - Sender: {sender_name} ({job_title} at {organization})\n\
//         - Goal: {goal}\n\
//         - Call to Action: {call_to_action}\n\
//         - Tone: {tone_preference}\n\
//         - Key Points: {key_points}\n\n\
//         Write a well-structured email with an introduction, main points, call to action, and closing.",
//         tone = tone,
//         target_audience = email_context.target_audience,
//         sender_name = email_context.full_name,
//         job_title = email_context.job_title,
//         organization = email_context.organization.as_deref().unwrap_or(""),
//         goal = email_context.communication_goal,
//         call_to_action = email_context.call_to_action.as_deref().unwrap_or(""),
//         tone_preference = email_context.tone_preference,
//         key_points = key_points
//     )
// }

fn construct_email_prompt_chatgpt(
    email_context: &EmailContext,
    key_points: &str,
    tone: &str
) -> String {
    format!(
        "Generate a {tone} email as a JSON object with the following structure:
        {{
            \"subject\": \"email subject\",
            \"body\": \"HTML-formatted email body using tags supported in email platforms\"
        }}

        Requirements:
        - Body must use email-safe HTML and inline style (supported tags: <p>, <br>, <ul>, <li>, <a href>, <b>, <i>, <h1>-<h3>)
        - Avoid unsupported elements: <div>, external CSS, JavaScript
        - Include proper semantic structure
        - Links must use full https URLs

        Context:
        - Recipient: {target_audience}
        - Sender: {sender_name} ({job_title} at {organization})
        - Goal: {goal}
        - Call to Action: {call_to_action}
        - Tone Preferences: {tone_preference}
        - Key Points: {key_points}

        Structure the email with:
        1. Introduction paragraph
        2. Main content (use <ul> for lists where appropriate)
        3. Clear call to action section
        4. Closing/signature

        Output only valid JSON without Markdown formatting.
        Example:
        {{
            \"subject\": \"...\",
            \"body\": \"<p>...</p><ul><li>...</li></ul><p>... <a href=\\\"https://...\\\">...</a></p>\"
        }}",
        tone = tone,
        target_audience = email_context.target_audience,
        sender_name = email_context.full_name,
        job_title = email_context.job_title,
        organization = email_context.organization.as_deref().unwrap_or(""),
        goal = email_context.communication_goal,
        call_to_action = email_context.call_to_action.as_deref().unwrap_or(""),
        tone_preference = email_context.tone_preference,
        key_points = key_points
    )
}

//Add audience and email length Here also
fn construct_email_prompt_chatgpt_without_context(
    email_context: &EmailContext,
    key_points: &str,
    tone: &str
) -> String {
    format!(
        "Generate an email as a JSON object with the following structure:
        {{
            \"subject\": \"email subject\",
            \"body\": \"HTML-formatted email body using tags supported in email platforms\"
        }}

        Requirements:
        - Body must use email-safe HTML and inline style (supported tags: <p>, <br>, <ul>, <li>, <a href>, <b>, <i>, <h1>-<h3>)
        - Avoid unsupported elements: <div>, external CSS, JavaScript
        - Include proper semantic structure
        - Links must use full https URLs

        Context:
        - Key Points: {key_points}

        Structure the email with:
        1. Introduction paragraph
        2. Main content (use <ul> for lists where appropriate)
        3. Clear call to action section
        4. Closing/signature

        Output only valid JSON without Markdown formatting.
        Example:
        {{
            \"subject\": \"...\",
            \"body\": \"<p>...</p><ul><li>...</li></ul><p>... <a href=\\\"https://...\\\">...</a></p>\"
        }}",
        key_points = key_points
    )
}

pub async fn email_subject_generator_function_chatgpt(
    email_body: &str
) -> Result<String, Box<dyn Error>> {
    let client = Client::new();
    let api_key = "your_openai_api_key";

    // Prompt for generating a subject line based on the email content
    let subject_prompt =
        format!("You are an assistant tasked with creating a concise and compelling email subject line.\n\n\
        Based on the following email content, generate an appropriate subject line.\n\n\
        Email Content:\n{}\n\nSubject:", email_body);

    // Call OpenAI API to generate the subject line
    let response = client
        .post("https://api.openai.com/v1/completions")
        .header("Authorization", format!("Bearer {}", api_key))
        .json(
            &json!({
            "model": "text-davinci-003",
            "prompt": subject_prompt,
            "temperature": 0.7,
            "max_tokens": 60,   // Limit tokens for concise subject lines
            "top_p": 0.9,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.3
        })
        )
        .send().await?;

    // Parse the response to get the generated subject line
    let response_json: serde_json::Value = response.json().await?;
    let subject_line = response_json["choices"][0]["text"]
        .as_str()
        .unwrap_or("Failed to generate subject line")
        .trim()
        .replace("Subject:", "")
        .trim()
        .to_string();

    println!("Generated Subject: {}", subject_line);
    Ok(subject_line)
}

/// Helper function to generate text based on an input context
pub async fn email_gnerator_function(
    key_points: &str,
    tone: &str,
    email_context: &EmailContext
) -> Result<String, Box<dyn Error>> {
    let prompt = construct_email_prompt(email_context);

    // Define the model configuration
    // let config = TextGenerationConfig {
    //     model_type: ModelType::GPT2,
    //     max_length: Some(250),   // Set reasonable max length for emails
    //     do_sample: true,         // Sampling for more natural output
    //     temperature: 0.7,        // Balanced randomness
    //     top_p: 0.9,              // Nucleus sampling for coherence
    //     repetition_penalty: 1.1, // Avoid repetition
    //     early_stopping: false,   // Do not stop early, rely on max_length and natural stop
    //     ..Default::default()
    // };

    // Initialize the TextGenerationModel in a separate blocking task
    // let model = task::spawn_blocking(|| TextGenerationModel::new(config))
    //     .await
    //     .map_err(|e| format!("Failed to join async task: {}", e))? // Convert JoinError to String
    //     .map(|model| Arc::new(Mutex::new(model)))
    //     .map_err(|e| {
    //         eprintln!("Failed to initialize the text generation model: {:?}", e);
    //         Box::new(e) as Box<dyn Error>
    //     })?;

    // Use a separate scope to limit the lifetime of the lock
    // let locked_model = model.lock().await;

    // // Generate the email body
    // let prompt = construct_email_prompt(email_context);
    // let generated_emails = locked_model.generate(&[prompt], None);

    // println!("Generate Email");

    // let email_body = generated_emails.get(0).cloned().unwrap_or_default();

    // Construct a prompt for generating the subject line
    let subject_prompt = format!(
        "You are an assistant tasked with creating a concise and compelling email subject line.\n\n\
        Based on the following email content, generate an appropriate subject line.\n\n\
        Email Content:\n\nSubject:"
        // email_body
    );

    // // Log the generated email and subject
    println!("Generated Email Body: {}", subject_prompt);

    return Ok("".to_string());
}

pub async fn email_subjet_gnerator_function(email_body: &str) -> Result<String, Box<dyn Error>> {
    // Define the model configuration
    // let config = TextGenerationConfig {
    //     model_type: ModelType::GPT2,
    //     max_length: Some(250),   // Set reasonable max length for emails
    //     do_sample: true,         // Sampling for more natural output
    //     temperature: 0.7,        // Balanced randomness
    //     top_p: 0.9,              // Nucleus sampling for coherence
    //     repetition_penalty: 1.1, // Avoid repetition
    //     early_stopping: false,   // Do not stop early, rely on max_length and natural stop
    //     ..Default::default()
    // };

    // Initialize the TextGenerationModel in a separate blocking task
    // let model = task::spawn_blocking(|| TextGenerationModel::new(config))
    //     .await
    //     .map_err(|e| format!("Failed to join async task: {}", e))? // Convert JoinError to String
    //     .map(|model| Arc::new(Mutex::new(model)))
    //     .map_err(|e| {
    //         eprintln!("Failed to initialize the text generation model: {:?}", e);
    //         Box::new(e) as Box<dyn Error>
    //     })?;

    // Use a separate scope to limit the lifetime of the lock
    // let locked_model = model.lock().await;

    // // Generate the email body
    // let generated_emails = locked_model.generate(&[email_body], None);

    // let email_subject = generated_emails.get(0).cloned().unwrap_or_default();

    // return Ok(email_subject);
    Ok("".to_string())
}

fn construct_email_prompt(email_context: &EmailContext) -> String {
    let mut prompt = String::new();

    // Include the sender's information
    prompt.push_str("You are an assistant helping to write an email.\n");
    prompt.push_str("The sender's details are as follows:\n");
    prompt.push_str(&format!("Full Name: {}\n", email_context.full_name));
    prompt.push_str(&format!("Job Title: {}\n", email_context.job_title));
    prompt.push_str(&format!("Language Style: {}\n", email_context.language_style));
    prompt.push_str(
        &format!("Email Length Preference: {}\n", email_context.email_length_preference)
    );

    // Include the email context
    prompt.push_str("\nCompose an email with the following details:\n");
    prompt.push_str(&format!("Tone: {}\n", email_context.tone_preference));
    prompt.push_str("Key Points:\n");
    prompt.push_str(&format!("- {}\n", email_context.key_points));

    // Instructions for the AI
    prompt.push_str(
        "\nPlease generate a professional email that includes an introduction, body, call to action, and closing phrases based on the above information. Do not include a subject line.\n"
    );

    prompt
}

fn construct_email_reply_prompt(email_context: &EmailContext, email: &str) -> String {
    format!(
        "Generate a reply email with the following structure:
        {{
            \"subject\": \"HTML-formatted reply subject using tags supported in email platforms\"
            \"body\": \"HTML-formatted email body using tags supported in email platforms\"
        }}

        for this email: {email}

        Requirements:
        - Body must use email-safe HTML and inline style (supported tags: <p>, <br>, <ul>, <li>, <a href>, <b>, <i>, <h1>-<h3>)
        - Avoid unsupported elements: <div>, external CSS, JavaScript
        - Include proper semantic structure
        - Links must use full https URLs

        Context:
        - Recipient: {target_audience}
        - Sender: {sender_name} ({job_title} at {organization})
        - Goal: {goal}
        - Call to Action: {call_to_action}
        - Tone Preferences: {tone_preference}

        Structure the email with:
        1. Introduction paragraph
        2. Main content (use <ul> for lists where appropriate)
        3. Clear call to action section
        4. Closing/signature

        Output only valid string without Markdown formatting.
        Do not ever put any text with the response just answer with the json (this is very important)
        ",
        target_audience = email_context.target_audience,
        sender_name = email_context.full_name,
        job_title = email_context.job_title,
        organization = email_context.organization.as_deref().unwrap_or(""),
        goal = email_context.communication_goal,
        call_to_action = email_context.call_to_action.as_deref().unwrap_or(""),
        tone_preference = email_context.tone_preference,
        email = email
    )
}

#[derive(Debug, Deserialize, Serialize)]
pub struct EmailAiResponse {
    pub subject: String,
    pub body: String,
}

pub async fn ai_generate_email_reply(
    email_context: &EmailContext,
    email: &str
) -> Result<EmailAiResponse, Box<dyn Error>> {
    let client = Client::new();
    let api_key =
        "********************************************************************************************************************************************************************"; //Oway Operator call

    // Construct the prompt with email context details
    let prompt = construct_email_reply_prompt(email_context, email);

    println!("Prompt => {}", prompt);
    // Call the OpenAI API to generate the email
    let response = client
        .post("https://api.openai.com/v1/completions")
        .header("Authorization", format!("Bearer {}", api_key))
        .json(
            &json!({
            "model": "gpt-3.5-turbo-instruct",//"text-davinci-003",  // Use GPT-3 model
            "prompt": prompt,
            "temperature": 0.7,           // Control randomness
            "max_tokens": 1000,            // Set reasonable max length for emails
            "top_p": 0.9,                 // Use nucleus sampling for coherence
            "frequency_penalty": 0.1,     // Reduce repetition
            "presence_penalty": 0.3       // Encourage diverse language
        })
        )
        .send().await?;

    // Parse the response to get the generated email content
    let response_json: serde_json::Value = response.json().await?;
    // println!("Response => {:?}", response_json);
    let email_body = response_json["choices"][0]["text"]
        .as_str()
        .unwrap_or("Failed to generate email")
        .to_string();

    println!("Generated Email Body: {}", email_body);
    let result: EmailAiResponse = sanitize_and_parse(&email_body).unwrap();
    println!("Reply json => {:?}", result);
    // Ok(sanitize_json(&email_body))
    Ok(result)
}

fn construct_reply_safety_prompt(email_context: &EmailContext, email: &str, reply: &str) -> String {
    format!(
        "You are an email quality assurance assistant for an AI email agent.
        You receive an AI-generated email reply and must assess it for tone, clarity, risk, and appropriateness.
        Evaluate the reply in context of the original email and the user's communication preferences.
        Identify any vague, risky, informal, or inappropriate content. Return structured feedback on whether the reply can be sent automatically or if it needs human review.

        Original email: {email}
        the reply: {reply}

        Requirements:
        - Body must use email-safe HTML and inline style (supported tags: <p>, <br>, <ul>, <li>, <a href>, <b>, <i>, <h1>-<h3>)
        - Avoid unsupported elements: <div>, external CSS, JavaScript
        - Include proper semantic structure
        - Links must use full https URLs

        Context:
        - Recipient: {target_audience}
        - Sender: {sender_name} ({job_title} at {organization})
        - Goal: {goal}
        - Call to Action: {call_to_action}
        - Tone Preferences: {tone_preference}

        Structure of the response :
        {{
            'requires_user_input': boolean,
            'confidence_score': float, // percentage 0 to 1
            'risk_flags': [string],
            'recommendations': [string]
        }}

        Output only sanitized valid JSON without Markdown formatting .
        Do not ever put any text with the response just answer with the json (this is very important)
        ",
        target_audience = email_context.target_audience,
        sender_name = email_context.full_name,
        job_title = email_context.job_title,
        organization = email_context.organization.as_deref().unwrap_or(""),
        goal = email_context.communication_goal,
        call_to_action = email_context.call_to_action.as_deref().unwrap_or(""),
        tone_preference = email_context.tone_preference,
        email = email,
        reply = reply
    )
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ReplyAssessorResponse {
    pub requires_user_input: bool,
    pub confidence_score: f32,
    pub risk_flags: Vec<String>,
    pub recommendations: Vec<String>,
}

pub async fn check_reply_safety(
    email_context: &EmailContext,
    email: &str,
    reply: &str
) -> Result<ReplyAssessorResponse, Box<dyn Error>> {
    let client = Client::new();
    let api_key =
        "********************************************************************************************************************************************************************"; //Oway Operator call

    // Construct the prompt with email context details
    let prompt = construct_reply_safety_prompt(email_context, email, reply);

    println!("Safety Prompt => {}", prompt);
    // Call the OpenAI API to generate the email
    let response = client
        .post("https://api.openai.com/v1/completions")
        .header("Authorization", format!("Bearer {}", api_key))
        .json(
            &json!({
            "model": "gpt-3.5-turbo-instruct",//"text-davinci-003",  // Use GPT-3 model
            "prompt": prompt,
            "temperature": 0.7,           // Control randomness
            "max_tokens": 1000,            // Set reasonable max length for emails
            "top_p": 0.9,                 // Use nucleus sampling for coherence
            "frequency_penalty": 0.1,     // Reduce repetition
            "presence_penalty": 0.3       // Encourage diverse language
        })
        )
        .send().await?;

    // Parse the response to get the generated email content
    let response_json: serde_json::Value = response.json().await?;
    // println!("Response => {:?}", response_json);
    let email_body = response_json["choices"][0]["text"]
        .as_str()
        .unwrap_or("Failed to generate email")
        .to_string();
    // let email_body = sanitize_and_parse(&email_body);
    println!("Generated Email Body String: {:?}", email_body);
    let result: ReplyAssessorResponse = sanitize_and_parse(&email_body).unwrap();
    println!("Generated Email Body JSON: {:?}", result);
    // Ok(sanitize_json(&email_body))
    Ok(result)
}
