# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/parser@^7.23.5", "@babel/parser@^7.25.3":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.26.2.tgz"
  integrity sha512-DWMCZH9WA4Maitz2q21SRKHo9QXZxkDsbNZoVD62gusNtNBBqDg9i7uOhASfTfIGNzW+O+r7+jAlM8dwphcJKQ==
  dependencies:
    "@babel/types" "^7.26.0"

"@babel/types@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.26.0.tgz"
  integrity sha512-Z/yiTPj+lDVnF7lWeKCIJzaIkI0vYO87dMpZ4bg4TDrFe4XXLFWL1TbXU27gBP3QccxV9mZICCrnjnYlJjXHOA==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@ckeditor/ckeditor5-adapter-ckfinder@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-44.2.1.tgz"
  integrity sha512-5scuKCJk+rFMg0gzNn1BQrEHJKDObAbbS34Yq7fs0050Cj2PIBiPV559RKqTpO4QERwyegCiO3jYfTZnN/ue+Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-upload" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-alignment@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-alignment/-/ckeditor5-alignment-44.2.1.tgz"
  integrity sha512-i9YVg0Slg0Qg+2pamy+cv/As2BixTptsoJVjQdQbhCCD0CbdOWOfysKwLF76O6ni/pMkJrkK7+IoNtX2ISa/cw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-autoformat@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-44.2.1.tgz"
  integrity sha512-z4k9tn/vO6X9wAZQZbCFlwmLoECXROleZw2ZzF5kOIm99tTBrap8kgNoVnm7g7SOG4toAEBJhntv0GCqUWy23w==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-heading" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-autosave@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autosave/-/ckeditor5-autosave-44.2.1.tgz"
  integrity sha512-vijtwJDeLbTmNL+anfDpgLSqn50iN5iEivKftdQ/b5Z9xcI9RO/fx5OAcZbi6hJZk1ovh6vE1IaEKUFuYvPOOg==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-basic-styles@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-44.2.1.tgz"
  integrity sha512-Y3d1lSVzY5uDUNQP9sw4As0/qU41OUQ7X2ZAVPNhtKhS8oCpTZH+GkcWOMZdhAFTkjDA/5PqtQ42z2NdEa8RQA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-block-quote@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-44.2.1.tgz"
  integrity sha512-v5jeKL7Gfwp7TJ5mp7QGunW0MwSGzfsRuNvawjXb5LLRT6aXqKBTiMxHypZAvlDTaucHiNSntYft/Bs0Tuf4zA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-bookmark@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-bookmark/-/ckeditor5-bookmark-44.2.1.tgz"
  integrity sha512-pJfHIWsuTEYc8k2WvuMiBECdVlvkPHj1gqbDvdyBKjD2YeK14TR02m0s8wVeP67XGD15inPG8QJvO72N7CnMUg==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-ckbox@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-44.2.1.tgz"
  integrity sha512-I0NPyY6JNWyKkCkCYzevgu2qL9uyRO+szewxT7ymEJzGLSKiwlL2skVp3Lhips+EOzRNDPhkkK9M1QnUUKUibA==
  dependencies:
    "@ckeditor/ckeditor5-cloud-services" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-image" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-upload" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    blurhash "2.0.5"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-ckfinder@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-44.2.1.tgz"
  integrity sha512-kBdtcmz8FD66Yj5jHCYlwM4yq1d/OtkKmyPVE7K3Qusvw+6OmGkyUP7z8GpKHtIlA6HmqR6+bfx/JnnhMSrG6A==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-image" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-clipboard@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-44.2.1.tgz"
  integrity sha512-c276UiNp9kOi4WdojI36DZ/q9LhVFnuAhE/TGzkJczyib0riJjaddd1XDXoCe0c3A11kGdbAueMeQeF+dZwKHQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-cloud-services@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-44.2.1.tgz"
  integrity sha512-op9ryEg4biYTrxKsp2lEy5OngXbVwZrQQx6sdJ5u0oca6FbhvzJKLZsitnp8HYisiBGts7cWmHgmOAqljt7HYA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-code-block@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-code-block/-/ckeditor5-code-block-44.2.1.tgz"
  integrity sha512-2fXvieUt9jHdzz6uIE4cVy3xD1g5YV8LHlwIRFjP5TTyTrm9zQYAxTzhEpfkRCebcxlY52rW0V2U4HM2jp/6aw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-core@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-44.2.1.tgz"
  integrity sha512-ZZjOW1TMItsAmkZCz26NytOL13kNSTIAVOLNeHL/k8CCVprpKP54yXY18EhBcuNn8Ecugo0M5Q/XVCZSX3kSvg==
  dependencies:
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-watchdog" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-easy-image@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-44.2.1.tgz"
  integrity sha512-fF8kosM9DG0mIaMhZf1U7WfLr4O6w+Y9FeEyhPw2gfpMfSV96iu/nFnFTEGllciIjXoqJ+GexwAtINgHMta7lA==
  dependencies:
    "@ckeditor/ckeditor5-cloud-services" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-upload" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-editor-balloon@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-44.2.1.tgz"
  integrity sha512-jzZP7OEA6xdXWX3RqY+ExZTp8wy6yar5KxEYkEl7z5PpGtAoQueh+f6leDU6MhG5vSw1oZHqBtn9FScqwUNJsA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-classic@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-44.2.1.tgz"
  integrity sha512-EvQW24VBP17C1xbGgbDZEfZUi2rn+AmMOOooPW7v0PQKH5+DtyIluOMnGwhjPSdDSL2njLT5+r2zof3D/A3pcQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-decoupled@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-decoupled/-/ckeditor5-editor-decoupled-44.2.1.tgz"
  integrity sha512-egRx2LEQyu48kXXWwxBc7mI9GrDKABuQSrlUANEqNCgXu6ahATwW6XbLWnyLzv8mR6rNDSXWixVmnfCvN+0ogQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-inline@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-44.2.1.tgz"
  integrity sha512-B6egt/98ZAAUwiYp0cIJ1V3GbAkGRqzVOFvylMig/gUqdW6fqaqdkJNyvN9jyG59WkB7b/zQd7bKPjPcLPnL1g==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-editor-multi-root@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-multi-root/-/ckeditor5-editor-multi-root-44.2.1.tgz"
  integrity sha512-1wb4dU1vhSK5zuxCOtqxcYPPB74I9sieHIpZQt4Zrhwk1ZxbpzaVyznIjnRQ3/6DEZccY+57stg/NmjwwiTaMw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-emoji@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-emoji/-/ckeditor5-emoji-44.2.1.tgz"
  integrity sha512-A5nSVwuCGikEXNnTXswDwfZeLioqtoYL96IaU/tMjWWIsRXXwGpNLaXiIhehrCTpdhM2L6QlHzz5+Yb+YzphOA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-mention" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    fuzzysort "3.1.0"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-engine@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-44.2.1.tgz"
  integrity sha512-zY6uDsPPxElHtwu99KpDLcVK3nCnHqX5ys461MwEl5sUuiwLmT7xlsPNANpoKb/MNX4KfPwdqiWBv1PbfQfRUg==
  dependencies:
    "@ckeditor/ckeditor5-utils" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-enter@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-44.2.1.tgz"
  integrity sha512-yre/+bClJiAq0ZTMxA4WT1DWqcmxVwtpIUWzrCzADa6d8rPKhk59/iypNF/IJ1WPO9FmhECxRPbOxKdHH1vJGA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"

"@ckeditor/ckeditor5-essentials@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-44.2.1.tgz"
  integrity sha512-xaWCiLT5CG5l0zlWt0vbBAIGN+fFva5+9tBh0kf5NnwrU7P9e4ZMmY+S9FLTfIKMPe5wYzhWHQJO8rW98LZl2A==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-select-all" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-undo" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-find-and-replace@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-find-and-replace/-/ckeditor5-find-and-replace-44.2.1.tgz"
  integrity sha512-MvdyxLoGFrzbK01FIyFHv7Slcxs/BfBia7IsnJTldFFMp2DIlckhG7nROVC+6gtxVTw/4xphSWPUNxBjBBXApw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-font@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-font/-/ckeditor5-font-44.2.1.tgz"
  integrity sha512-iuf2C7vh6U8Pi+qcyUUQVjF4Rws1mMCkWLDMVz8JW1cvZ+boNjMkhNAx4NK76arImY9qgsYa44MBK59An/640w==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-heading@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-44.2.1.tgz"
  integrity sha512-M3i+zXIW6HOtGjJWZfGYmWkg2/U4mewMMj8XbEq/GRcaTCJxpqlw7590DdMfIhi0NqQwQ9zFpp+pXcUFPBcqWA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-paragraph" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-highlight@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-highlight/-/ckeditor5-highlight-44.2.1.tgz"
  integrity sha512-kuwk2DeAeGmeRl5ntRiLJqvvK5VIgU5MHVbpDVSMvalBMFFBlSwJN6xaxygCuu/rHIUaE4rFKVDJZS8o/3hToQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-horizontal-line@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-horizontal-line/-/ckeditor5-horizontal-line-44.2.1.tgz"
  integrity sha512-lrtMHa5cu93MNJqiGvrE8kLCtpI6jqElIMr3ddbfGwwpwhkNrdaV6ThWyEp4LOHACYuwpw0QVN6Xj0VcohYygQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-html-embed@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-embed/-/ckeditor5-html-embed-44.2.1.tgz"
  integrity sha512-JnpQYc+aVPqpdu4L/FqxRpfOeOPv5OjH/jfqRbCpFsVk+MlXGjb34hwS8emnk0oRsXkzMjd8pNNn2GWrBEN8Ig==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-html-support@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-html-support/-/ckeditor5-html-support-44.2.1.tgz"
  integrity sha512-DldRFGKtAHmiB+kcBw4wfu5AaxfGg2GyK4XCAiebIirNBkkbcuep/zmcSBRyG0s99cW90PAA5+WtJEPNzoIEEA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-heading" "44.2.1"
    "@ckeditor/ckeditor5-image" "44.2.1"
    "@ckeditor/ckeditor5-list" "44.2.1"
    "@ckeditor/ckeditor5-table" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-image@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-44.2.1.tgz"
  integrity sha512-uh2LJPUDEbkOI2wb4aUJQFXF3eSrv16rZLMdTQStL022xkkbn7bAgkGHQ6hUNpUmJdT25KP5z4cNq8HEITbWSA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-undo" "44.2.1"
    "@ckeditor/ckeditor5-upload" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-indent@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-44.2.1.tgz"
  integrity sha512-ZApH7zXQzJzAYe3kHKGmrtwEVui5FfHEW5kOCiVSc/nIYuKZknGRsBBYnh9RjINbOBvdqKlGY7TJjilZDAJXkg==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-heading" "44.2.1"
    "@ckeditor/ckeditor5-list" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-integrations-common@^2.2.2":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-integrations-common/-/ckeditor5-integrations-common-2.2.3.tgz"
  integrity sha512-92kQWQj1wiABF7bY1+J79Ze+WHr7pwVBufn1eeJLWcTXbPQq4sAolfKv8Y8Ka9g69mdyE9+GPWmGFYDeQJVPDg==

"@ckeditor/ckeditor5-language@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-language/-/ckeditor5-language-44.2.1.tgz"
  integrity sha512-vSoGRqwOzvpuJ0nXn4P1gsO3R01uQWdkCdZCq/Xor6zOVfXALx4Jq6GzOxqtf23I7dvZk0qEDSM0eZTwRGtPmw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-link@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-44.2.1.tgz"
  integrity sha512-DKkv/5oihag23CsRRxRr9hVQiIND9RFRCTXZ/gw5sMpBcTZ34IfzVqHAxJXoCmR6VKLSI14kjbNPtyOx/u5Sjw==
  dependencies:
    "@ckeditor/ckeditor5-bookmark" "44.2.1"
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-image" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-list@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-44.2.1.tgz"
  integrity sha512-YlgdunxlRd7WZn4haGodPzkJGlBCDn4vCD5matZVoLmUbcCq0fYXwgDlcY/dAWoSGtEtNV0JGMhaHljQ+3EAzw==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-markdown-gfm@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-markdown-gfm/-/ckeditor5-markdown-gfm-44.2.1.tgz"
  integrity sha512-SEW4jKRhuryao5z7mFoctRnovHdHv5Ud1vq+HXQ/pypxHElv+WKxw3yeU7Y2EGIBhReiLrdkE/alLZ8RxFNFiQ==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@types/marked" "4.3.2"
    "@types/turndown" "5.0.5"
    ckeditor5 "44.2.1"
    marked "4.0.12"
    turndown "7.2.0"
    turndown-plugin-gfm "1.0.2"

"@ckeditor/ckeditor5-media-embed@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-44.2.1.tgz"
  integrity sha512-V7U02KkdEAV9uLirIRI68Ec54aARrOEqsGvnu0Y6J2fFvqPYwdOTINwyytrykMW+o4vJ0ZKwVTE16N0O3ugnmA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-undo" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-mention@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-mention/-/ckeditor5-mention-44.2.1.tgz"
  integrity sha512-qRffnNV2EEbBvRC5OsrhI3i1quWVWi0WX6+zf9YoQCNeABg3aX4va+seK41Z5cjOKIYy+6smXcMGcWOTU3Y44w==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-minimap@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-minimap/-/ckeditor5-minimap-44.2.1.tgz"
  integrity sha512-texkHgknOK8S7zWChdwZJyR1eOPrjYJZ8NC4KpE55eQK7fJ+iRu8PZbhM02T1EwepS1KTjp45ELQ062gZkL5Ug==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-page-break@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-page-break/-/ckeditor5-page-break-44.2.1.tgz"
  integrity sha512-D+6RUEQ6KIviPFysF5O+bxOyWfTADuSqFvVMvnh/oiz4lq9fIeTWEaVXCh7rv8oQssP5shjSCVhGxUfL/7hfdw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-paragraph@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-44.2.1.tgz"
  integrity sha512-Hw3ROJBJi7JJmCXNFLVW6sIQCV3o7sdC6MxyZkn+YEGL5B0wEb5xXx2wtChwJabKswXkmaAv2nQj5YXuxLruRQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"

"@ckeditor/ckeditor5-paste-from-office@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-44.2.1.tgz"
  integrity sha512-UxESX3z++Z8umlQy6HU+VOhqApx+0DvmKhApLX8COSOdB4eoUYTBaUCSakXiOqXSAgISMXL93xy8Tr03mvhdxg==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-remove-format@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-remove-format/-/ckeditor5-remove-format-44.2.1.tgz"
  integrity sha512-8KkAg4FDAK40thtd+3SxGvz4BqvQYD5Kq2YWNhR8P2jeXAXmVvcQTK0Nulm0bGceKLiCmrd8VTu2hWHkvC0oiA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-restricted-editing@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-restricted-editing/-/ckeditor5-restricted-editing-44.2.1.tgz"
  integrity sha512-4jWutnvhyGUr/DdwF3SvKJXVI/CVor5rw0psm/e4EXUTIzprBjYGHSzg0SZnjDQbg6cfWs2iEPEMb+HHSGkf+w==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-select-all@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-44.2.1.tgz"
  integrity sha512-gBhFSNqhnjReR2vCafJfCtagA7uyQsqXswzp1ejPkoLf0lhn8unyxm0vMjUWDqxsL19HlRJXib4W8nN8gs/mlA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"

"@ckeditor/ckeditor5-show-blocks@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-show-blocks/-/ckeditor5-show-blocks-44.2.1.tgz"
  integrity sha512-QxNYbNLCXB/JeqLv9H/sLXrXzrbxHIzgPKzBqNL2kzJldTsB48chy+UFsrGSAigjc0Bce31hSLoPlkI1iNw+SQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-source-editing@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-source-editing/-/ckeditor5-source-editing-44.2.1.tgz"
  integrity sha512-bFzQJmpiF+qgauMuFd2q0YTg3yHtRkcU9/lF1vvIlQJ2P9mSoxhClah8tCL3mTVaL1uOeWwXhkKipuaDKJLKMg==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-theme-lark" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-special-characters@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-special-characters/-/ckeditor5-special-characters-44.2.1.tgz"
  integrity sha512-vPrZXeJqZGSykgJPKUYK8KE5oYuglAdEROFEYhyqAbwKwgAPvQjKgUd6b2Cv9Ufdq0pp2l1VDJcDA6nvaqiA9Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"

"@ckeditor/ckeditor5-style@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-style/-/ckeditor5-style-44.2.1.tgz"
  integrity sha512-EEBgqYpXnMXK533ZFF9SgADZlN08MJvtkKB5Ac3A6Ba6dNlD31/wydnYB4iTosRwsKHwrv+0jn6GFmFQxe3HCA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-html-support" "44.2.1"
    "@ckeditor/ckeditor5-list" "44.2.1"
    "@ckeditor/ckeditor5-table" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-table@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-44.2.1.tgz"
  integrity sha512-dR+GD7wQlM9PxP42VYlz4cDf8J7zP8y6sP1Wegu9QCShw8ZoxKNk59y3ofZObNCfwZZ6BW9Su8reMXXcP5iufA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-theme-lark@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-theme-lark/-/ckeditor5-theme-lark-44.2.1.tgz"
  integrity sha512-ajaBlmEbJi3CVhTE/OfM0ZgWLv8OwaigEpQz9faZpYHs8xXZLd4bFwsWQMrDo46Y1mbkP6w1JswnCLMYUPofJA==
  dependencies:
    "@ckeditor/ckeditor5-ui" "44.2.1"

"@ckeditor/ckeditor5-typing@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-44.2.1.tgz"
  integrity sha512-xSk8Ti3NWC4SwzI9SiGgNB8qTlAYemjqNOsedCqkSnIApVn7Mp4RRCxy6HskTZg9zUTL3wUwN7znkWANulwHNQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-ui@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-44.2.1.tgz"
  integrity sha512-BKId+O2kXhFZ07tLX7Mdc9udwMDjEmpsE8mDTnbLaZO2Brj+qV9bNrq4LfV46iXGpO6xGeEaf0fBahwgE8xW6Q==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-editor-multi-root" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@types/color-convert" "2.0.4"
    color-convert "2.0.1"
    color-parse "1.4.2"
    lodash-es "4.17.21"
    vanilla-colorful "0.7.2"

"@ckeditor/ckeditor5-undo@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-44.2.1.tgz"
  integrity sha512-D4iWZAwF1TD3i+E/1X+tRRv+xDXd/NQyBpu7BDP5363Kk9ieD7EXU6uXx0E/ZFd5r8It2dsR0wI56t0HytXxtA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"

"@ckeditor/ckeditor5-upload@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-44.2.1.tgz"
  integrity sha512-38E3eWNM84xsChaeNJZO+CR2DY3+eCFLjX4Dl3oX4f8ZZVGMUC4EP5VbKKw/YDUN6NCfIRdIMWUR3LWd+f6pWw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"

"@ckeditor/ckeditor5-utils@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-44.2.1.tgz"
  integrity sha512-e15n83g7IFUrfANHqcsW/hfiNhuOl18jzJ/BHsAdSH2vGNel2RTsvfkDR7Nj/X1DZk3L2y8sNVUcAiYBD6tdhg==
  dependencies:
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@types/lodash-es" "4.17.12"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-vue@^7.3.0":
  version "7.3.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-vue/-/ckeditor5-vue-7.3.0.tgz"
  integrity sha512-OM8VW2bf5cXWKKaSr2eS1BhjzPmvkC2Jp/rWFdjU8wi4hhcKVJ5QqMepDguDcC+PHThaLec45WIrQTeLCb2AaA==
  dependencies:
    "@ckeditor/ckeditor5-integrations-common" "^2.2.2"
    lodash-es "^4.17.21"

"@ckeditor/ckeditor5-watchdog@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-44.2.1.tgz"
  integrity sha512-qN0Lj67JvI/vcDkEKq4QIo/if5Tq+e3nXTnUPxMa/HScAv6AIeAACrJqNgaYO44Zza/mAnm/9OomKk2zy8YRAw==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-editor-multi-root" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-widget@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-44.2.1.tgz"
  integrity sha512-GKG35h13EeZQ69py/lTXg3/uzTpe2Bk6uollcYGEGhbBILLPievWkuvI+7y0Ib+x7gm8gaWcuzVWjte+OOHLCg==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-word-count@44.2.1":
  version "44.2.1"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-word-count/-/ckeditor5-word-count-44.2.1.tgz"
  integrity sha512-zntBOyZrV3zATHI1hpBN1QJ7uPELDa8vUd45Giyt8g6aZDyltdH2eEET5ftfRwACr3EZeuuTeuUNM5nDzmILgA==
  dependencies:
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    ckeditor5 "44.2.1"
    lodash-es "4.17.21"

"@esbuild/win32-x64@0.18.20":
  version "0.18.20"
  resolved "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz"
  integrity sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==

"@fortawesome/fontawesome-common-types@6.6.0":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.6.0.tgz"
  integrity sha512-xyX0X9mc0kyz9plIyryrRbl7ngsA9jz77mCZJsUkLl+ZKs0KWObgaEBoSgQiYWAsSmjz/yjl0F++Got0Mdp4Rw==

"@fortawesome/fontawesome-svg-core@^6.3.0", "@fortawesome/fontawesome-svg-core@~1 || ~6":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.6.0.tgz"
  integrity sha512-KHwPkCk6oRT4HADE7smhfsKudt9N/9lm6EJ5BVg0tD1yPA5hht837fB87F8pn15D8JfTqQOjhKTktwmLMiD7Kg==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.6.0"

"@fortawesome/free-regular-svg-icons@^6.3.0":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@fortawesome/free-regular-svg-icons/-/free-regular-svg-icons-6.6.0.tgz"
  integrity sha512-Yv9hDzL4aI73BEwSEh20clrY8q/uLxawaQ98lekBx6t9dQKDHcDzzV1p2YtBGTtolYtNqcWdniOnhzB+JPnQEQ==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.6.0"

"@fortawesome/free-solid-svg-icons@^6.3.0":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.6.0.tgz"
  integrity sha512-IYv/2skhEDFc2WGUcqvFJkeK39Q+HyPf5GHUrT/l2pKbtgEIv1al1TKd6qStR5OIwQdN1GZP54ci3y4mroJWjA==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.6.0"

"@fortawesome/vue-fontawesome@^3.0.3":
  version "3.0.8"
  resolved "https://registry.npmjs.org/@fortawesome/vue-fontawesome/-/vue-fontawesome-3.0.8.tgz"
  integrity sha512-yyHHAj4G8pQIDfaIsMvQpwKMboIZtcHTUvPqXjOHyldh1O1vZfH4W03VDPv5RvI9P6DLTzJQlmVgj9wCf7c2Fw==

"@growthbunker/vueflags@^0.1.14":
  version "0.1.14"
  resolved "https://registry.npmjs.org/@growthbunker/vueflags/-/vueflags-0.1.14.tgz"
  integrity sha512-iw0r5QIHV9j2YDLf71m0RsZPIueVwGyBlYfqtzHZUtXJq+uBYERSmpZeTeGh6XhwFR63aIvOITvq6hRHBxdXLw==

"@headlessui/vue@^1.7.22":
  version "1.7.22"
  resolved "https://registry.npmjs.org/@headlessui/vue/-/vue-1.7.22.tgz"
  integrity sha512-Hoffjoolq1rY+LOfJ+B/OvkhuBXXBFgd8oBlN+l1TApma2dB0En0ucFZrwQtb33SmcCqd32EQd0y07oziXWNYg==
  dependencies:
    "@tanstack/vue-virtual" "^3.0.0-beta.60"

"@heroicons/vue@^2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@heroicons/vue/-/vue-2.1.5.tgz"
  integrity sha512-IpqR72sFqFs55kyKfFS7tN+Ww6odFNeH/7UxycIOrlVYfj4WUGAdzQtLBnJspucSeqWFQsKM0g0YrgU655BEfA==

"@iconify/json@^2.2.309":
  version "2.2.309"
  resolved "https://registry.npmjs.org/@iconify/json/-/json-2.2.309.tgz"
  integrity sha512-xgaPDmVNeHmggEDHrbyntqPw8YzqIf96XOOqC7HbbDS4lf4a6mvYVCDYINSZjsw2Wvak3CL7PZLQ9ZgqoypMtg==
  dependencies:
    "@iconify/types" "*"
    pathe "^1.1.2"

"@iconify/tailwind@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@iconify/tailwind/-/tailwind-1.2.0.tgz"
  integrity sha512-KgpIHWOTcRYw1XcoUqyNSrmYyfLLqZYu3AmP8zdfLk0F5TqRO8YerhlvlQmGfn7rJXgPeZN569xPAJnJ53zZxA==
  dependencies:
    "@iconify/types" "^2.0.0"

"@iconify/types@*", "@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@iconify/vue@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@iconify/vue/-/vue-4.3.0.tgz"
  integrity sha512-Xq0h6zMrHBbrW8jXJ9fISi+x8oDQllg5hTDkDuxnWiskJ63rpJu9CvJshj8VniHVTbsxCg9fVoPAaNp3RQI5OQ==
  dependencies:
    "@iconify/types" "^2.0.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@mixmark-io/domino@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@mixmark-io/domino/-/domino-2.2.0.tgz"
  integrity sha512-Y28PR25bHXUg88kCV7nivXrP2Nj2RueZ3/l/jdx6J9f8J4nsEGcgX0Qe6lt7Pa+J79+kPiJU3LguR6O/6zrLOw==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@preact/signals-core@^1.7.0":
  version "1.8.0"
  resolved "https://registry.npmjs.org/@preact/signals-core/-/signals-core-1.8.0.tgz"
  integrity sha512-OBvUsRZqNmjzCZXWLxkZfhcgT+Fk8DDcT/8vD6a1xhDemodyy87UJRJfASMuSD8FaAIeGgGm85ydXhm7lr4fyA==

"@preact/signals@^1.1.5", "@preact/signals@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@preact/signals/-/signals-1.3.0.tgz"
  integrity sha512-EOMeg42SlLS72dhoq6Vjq08havnLseWmPQ8A0YsgIAqMgWgx7V1a39+Pxo6i7SY5NwJtH4849JogFq3M67AzWg==
  dependencies:
    "@preact/signals-core" "^1.7.0"

"@schedule-x/calendar@^2.4.1", "@schedule-x/calendar@2.4.1":
  version "2.4.1"
  resolved "https://registry.npmjs.org/@schedule-x/calendar/-/calendar-2.4.1.tgz"
  integrity sha512-3r/UEFAqITTUA0uqWuV6CC1BJQt51cYLj/aXMUWoy4SAedBj7+qV7ne/D7SX0qefGMlu3gZaC5USHzBVdm6ZTA==

"@schedule-x/date-picker@2.4.1":
  version "2.4.1"
  resolved "https://registry.npmjs.org/@schedule-x/date-picker/-/date-picker-2.4.1.tgz"
  integrity sha512-N5yPTLg+zZGjj6AQzM8EBb/bS2me0NmcneizZ+YwD1g4294SIyGUsUch0LVmY93fGqDyL6BtFkbEOV9cwaaAJg==

"@schedule-x/events-service@^2.12.1":
  version "2.14.3"
  resolved "https://registry.npmjs.org/@schedule-x/events-service/-/events-service-2.14.3.tgz"
  integrity sha512-BY6kcxrOlxWr0M1QRg7H4bbM+N/1sdoOdO1gXuAc/PAFkddKIiDP/ozz1EZ2UreW1jCtyczRzhimEF0Tjlmzjw==

"@schedule-x/theme-default@^2.4.1":
  version "2.4.1"
  resolved "https://registry.npmjs.org/@schedule-x/theme-default/-/theme-default-2.4.1.tgz"
  integrity sha512-DrDuxgLXzAWcZMYQmW4SyMcESbKDW+YXVrAOcc4NlUhhsLkbJkngS1JDBiGBJiKzFI47FntJyGFTz5qPeZDpXw==

"@schedule-x/vue@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@schedule-x/vue/-/vue-2.3.1.tgz"
  integrity sha512-Aqm9EBU6/7G8gIVs4hg9KJK99C0YGzuQtIX9fZzPsjihB0WeLj4gE5Bfp9vSDHMvljJADWeJy3HIpfJMbmjgJw==

"@svgdotjs/svg.js@^3.2.4":
  version "3.2.4"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.js/-/svg.js-3.2.4.tgz"
  integrity sha512-BjJ/7vWNowlX3Z8O4ywT58DqbNRyYlkk6Yz/D13aB7hGmfQTvGX4Tkgtm/ApYlu9M7lCQi15xUEidqMUmdMYwg==

"@tailwindcss/forms@^0.5.3":
  version "0.5.3"
  resolved "https://registry.npmjs.org/@tailwindcss/forms/-/forms-0.5.3.tgz"
  integrity sha512-y5mb86JUoiUgBjY/o6FJSFZSEttfb3Q5gllE4xoKjAAD+vBrnIhE4dViwUuow3va8mpH4s9jyUbUbrRGoRdc2Q==
  dependencies:
    mini-svg-data-uri "^1.2.3"

"@tailwindcss/typography@^0.5.9":
  version "0.5.9"
  resolved "https://registry.npmjs.org/@tailwindcss/typography/-/typography-0.5.9.tgz"
  integrity sha512-t8Sg3DyynFysV9f4JDOVISGsjazNb48AeIYQwcL+Bsq5uf4RYL75C1giZ43KISjeDGBaTN3Kxh7Xj/vRSMJUUg==
  dependencies:
    lodash.castarray "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.merge "^4.6.2"
    postcss-selector-parser "6.0.10"

"@tanstack/virtual-core@3.5.0":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.5.0.tgz"
  integrity sha512-KnPRCkQTyqhanNC0K63GBG3wA8I+D1fQuVnAvcBF8f13akOKeQp1gSbu6f77zCxhEk727iV5oQnbHLYzHrECLg==

"@tanstack/vue-virtual@^3.0.0-beta.60":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@tanstack/vue-virtual/-/vue-virtual-3.5.0.tgz"
  integrity sha512-wvRQ8sFxn/NDr3WvI5XabhFovZ5MBmpEck2GHpTxYunmV63Ovpl30lRu6W5BPQo35a1GqDZ+Pvzlz6WDWRNqqw==
  dependencies:
    "@tanstack/virtual-core" "3.5.0"

"@tauri-apps/api@^2.0.0", "@tauri-apps/api@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tauri-apps/api/-/api-2.0.0.tgz"
  integrity sha512-moKgCp2EX7X5GiOx/G/bmoEpkFQVVmyS98UaJU4xUVzan+E1BdwlAKcbip+cGldshYOqL4JSwAEN1OkRXeug0Q==

"@tauri-apps/cli-win32-x64-msvc@2.5.0":
  version "2.5.0"
  resolved "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-2.5.0.tgz"
  integrity sha512-lj43EFYbnAta8pd9JnUq87o+xRUR0odz+4rixBtTUwUgdRdwQ2V9CzFtsMu6FQKpFQ6mujRK6P1IEwhL6ADRsQ==

"@tauri-apps/cli@^2.5.0":
  version "2.5.0"
  resolved "https://registry.npmjs.org/@tauri-apps/cli/-/cli-2.5.0.tgz"
  integrity sha512-rAtHqG0Gh/IWLjN2zTf3nZqYqbo81oMbqop56rGTjrlWk9pTTAjkqOjSL9XQLIMZ3RbeVjveCqqCA0s8RnLdMg==
  optionalDependencies:
    "@tauri-apps/cli-darwin-arm64" "2.5.0"
    "@tauri-apps/cli-darwin-x64" "2.5.0"
    "@tauri-apps/cli-linux-arm-gnueabihf" "2.5.0"
    "@tauri-apps/cli-linux-arm64-gnu" "2.5.0"
    "@tauri-apps/cli-linux-arm64-musl" "2.5.0"
    "@tauri-apps/cli-linux-riscv64-gnu" "2.5.0"
    "@tauri-apps/cli-linux-x64-gnu" "2.5.0"
    "@tauri-apps/cli-linux-x64-musl" "2.5.0"
    "@tauri-apps/cli-win32-arm64-msvc" "2.5.0"
    "@tauri-apps/cli-win32-ia32-msvc" "2.5.0"
    "@tauri-apps/cli-win32-x64-msvc" "2.5.0"

"@tauri-apps/plugin-clipboard-manager@~2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@tauri-apps/plugin-clipboard-manager/-/plugin-clipboard-manager-2.2.2.tgz"
  integrity sha512-bZvDLMqfcNmsw7Ag8I49jlaCjdpDvvlJHnpp6P+Gg/3xtpSERdwlDxm7cKGbs2mj46dsw4AuG3RoAgcpwgioUA==
  dependencies:
    "@tauri-apps/api" "^2.0.0"

"@tauri-apps/plugin-dialog@~2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@tauri-apps/plugin-dialog/-/plugin-dialog-2.2.2.tgz"
  integrity sha512-Pm9qnXQq8ZVhAMFSEPwxvh+nWb2mk7LASVlNEHYaksHvcz8P6+ElR5U5dNL9Ofrm+uwhh1/gYKWswK8JJJAh6A==
  dependencies:
    "@tauri-apps/api" "^2.0.0"

"@tauri-apps/plugin-shell@~2":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@tauri-apps/plugin-shell/-/plugin-shell-2.2.1.tgz"
  integrity sha512-G1GFYyWe/KlCsymuLiNImUgC8zGY0tI0Y3p8JgBCWduR5IEXlIJS+JuG1qtveitwYXlfJrsExt3enhv5l2/yhA==
  dependencies:
    "@tauri-apps/api" "^2.0.0"

"@types/color-convert@2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@types/color-convert/-/color-convert-2.0.4.tgz"
  integrity sha512-Ub1MmDdyZ7mX//g25uBAoH/mWGd9swVbt8BseymnaE18SU4po/PjmCrHxqIIRjBo3hV/vh1KGr0eMxUhp+t+dQ==
  dependencies:
    "@types/color-name" "^1.1.0"

"@types/color-name@^1.1.0":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@types/color-name/-/color-name-1.1.5.tgz"
  integrity sha512-j2K5UJqGTxeesj6oQuGpMgifpT5k9HprgQd8D1Y0lOFqKHl3PJu5GMeS4Y5EgjS55AE6OQxf8mPED9uaGbf4Cg==

"@types/country-telephone-data@^0.6.3":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@types/country-telephone-data/-/country-telephone-data-0.6.3.tgz"
  integrity sha512-XRooLIe/i42PGpW/dP8hyEP8oFHkCJCe7wYQcl28BaC4fmUCYdyK8adVlepnAH9aRlIqr0mW/c6KRIBj78kgeg==

"@types/lodash-es@4.17.12":
  version "4.17.12"
  resolved "https://registry.npmjs.org/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.17.15"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.15.tgz"
  integrity sha512-w/P33JFeySuhN6JLkysYUK2gEmy9kHHFN7E8ro0tkfmlDOgxBDzWEZ/J8cWA+fHqFevpswDTFZnDx+R9lbL6xw==

"@types/marked@^4.0.8", "@types/marked@4.3.2":
  version "4.3.2"
  resolved "https://registry.npmjs.org/@types/marked/-/marked-4.3.2.tgz"
  integrity sha512-a79Yc3TOk6dGdituy8hmTTJXjOkZ7zsFYV10L337ttq/rec8lRMDBpV7fL3uLx6TgbFCa5DU/h8FmIBQPSbU0w==

"@types/node@^18.7.10", "@types/node@>= 14":
  version "18.15.11"
  resolved "https://registry.npmjs.org/@types/node/-/node-18.15.11.tgz"
  integrity sha512-E5Kwq2n4SbMzQOn6wnmBjuK9ouqlURrcZDVfbo9ftDDTFt3nk7ZKK4GMOzoYgnpQJKcxwQw+lGaBvvlMo0qN/Q==

"@types/turndown@5.0.5":
  version "5.0.5"
  resolved "https://registry.npmjs.org/@types/turndown/-/turndown-5.0.5.tgz"
  integrity sha512-TL2IgGgc7B5j78rIccBtlYAnkuv8nUQqhQc+DSYV5j9Be9XOcm/SKOVRuA47xAVI3680Tk9B1d8flK2GWT2+4w==

"@types/uuid@^9.0.1":
  version "9.0.1"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-rFT3ak0/2trgvp4yYZo5iKFEPsET7vKydKF+VRCxlQ9bpheehyAJH89dAkaLEq/j/RZXJIqcgsmPJKUP1Z28HA==

"@types/vue-tel-input@^2.1.7":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@types/vue-tel-input/-/vue-tel-input-2.1.7.tgz"
  integrity sha512-s7bj9VBEQwfSaHDfWJ1KnQ+bgx0N+GT+y3pcsvoEqOZv4zJn/DX3CxNWnyBOQx8hwt3fGWp/aTm/X+fbq91Uiw==
  dependencies:
    vue "^2.0.0"

"@vitejs/plugin-vue@^4.0.0":
  version "4.1.0"
  resolved "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-4.1.0.tgz"
  integrity sha512-++9JOAFdcXI3lyer9UKUV4rfoQ3T1RN8yDqoCLar86s0xQct5yblxAE+yWgRnU5/0FOlVCpTZpYSBV/bGWrSrQ==

"@volar/language-core@1.3.0-alpha.0":
  version "1.3.0-alpha.0"
  resolved "https://registry.npmjs.org/@volar/language-core/-/language-core-1.3.0-alpha.0.tgz"
  integrity sha512-W3uMzecHPcbwddPu4SJpUcPakRBK/y/BP+U0U6NiPpUX1tONLC4yCawt+QBJqtgJ+sfD6ztf5PyvPL3hQRqfOA==
  dependencies:
    "@volar/source-map" "1.3.0-alpha.0"

"@volar/source-map@1.3.0-alpha.0":
  version "1.3.0-alpha.0"
  resolved "https://registry.npmjs.org/@volar/source-map/-/source-map-1.3.0-alpha.0.tgz"
  integrity sha512-jSdizxWFvDTvkPYZnO6ew3sBZUnS0abKCbuopkc0JrIlFbznWC/fPH3iPFIMS8/IIkRxq1Jh9VVG60SmtsdaMQ==
  dependencies:
    muggle-string "^0.2.2"

"@volar/typescript@1.3.0-alpha.0":
  version "1.3.0-alpha.0"
  resolved "https://registry.npmjs.org/@volar/typescript/-/typescript-1.3.0-alpha.0.tgz"
  integrity sha512-5UItyW2cdH2mBLu4RrECRNJRgtvvzKrSCn2y3v/D61QwIDkGx4aeil6x8RFuUL5TFtV6QvVHXnsOHxNgd+sCow==
  dependencies:
    "@volar/language-core" "1.3.0-alpha.0"

"@volar/vue-language-core@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@volar/vue-language-core/-/vue-language-core-1.2.0.tgz"
  integrity sha512-w7yEiaITh2WzKe6u8ZdeLKCUz43wdmY/OqAmsB/PGDvvhTcVhCJ6f0W/RprZL1IhqH8wALoWiwEh/Wer7ZviMQ==
  dependencies:
    "@volar/language-core" "1.3.0-alpha.0"
    "@volar/source-map" "1.3.0-alpha.0"
    "@vue/compiler-dom" "^3.2.47"
    "@vue/compiler-sfc" "^3.2.47"
    "@vue/reactivity" "^3.2.47"
    "@vue/shared" "^3.2.47"
    minimatch "^6.1.6"
    muggle-string "^0.2.2"
    vue-template-compiler "^2.7.14"

"@volar/vue-typescript@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@volar/vue-typescript/-/vue-typescript-1.2.0.tgz"
  integrity sha512-zjmRi9y3J1EkG+pfuHp8IbHmibihrKK485cfzsHjiuvJMGrpkWvlO5WVEk8oslMxxeGC5XwBFE9AOlvh378EPA==
  dependencies:
    "@volar/typescript" "1.3.0-alpha.0"
    "@volar/vue-language-core" "1.2.0"

"@vue/compiler-core@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.12.tgz"
  integrity sha512-ISyBTRMmMYagUxhcpyEH0hpXRd/KqDU4ymofPgl2XAkY9ZhQ+h0ovEZJIiPop13UmR/54oA2cgMDjgroRelaEw==
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/shared" "3.5.12"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@^3.2.47", "@vue/compiler-dom@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.12.tgz"
  integrity sha512-9G6PbJ03uwxLHKQ3P42cMTi85lDRvGLB2rSGOiQqtXELat6uI4n8cNz9yjfVHRPIu+MsK6TE418Giruvgptckg==
  dependencies:
    "@vue/compiler-core" "3.5.12"
    "@vue/shared" "3.5.12"

"@vue/compiler-sfc@^3.2.47", "@vue/compiler-sfc@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.12.tgz"
  integrity sha512-2k973OGo2JuAa5+ZlekuQJtitI5CgLMOwgl94BzMCsKZCX/xiqzJYzapl4opFogKHqwJk34vfsaKpfEhd1k5nw==
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/compiler-core" "3.5.12"
    "@vue/compiler-dom" "3.5.12"
    "@vue/compiler-ssr" "3.5.12"
    "@vue/shared" "3.5.12"
    estree-walker "^2.0.2"
    magic-string "^0.30.11"
    postcss "^8.4.47"
    source-map-js "^1.2.0"

"@vue/compiler-sfc@2.7.16":
  version "2.7.16"
  resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-2.7.16.tgz"
  integrity sha512-KWhJ9k5nXuNtygPU7+t1rX6baZeqOYLEforUPjgNDBnLicfHCoi48H87Q8XyLZOrNNsmhuwKqtpDQWjEFe6Ekg==
  dependencies:
    "@babel/parser" "^7.23.5"
    postcss "^8.4.14"
    source-map "^0.6.1"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/compiler-ssr@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.12.tgz"
  integrity sha512-eLwc7v6bfGBSM7wZOGPmRavSWzNFF6+PdRhE+VFJhNCgHiF8AM7ccoqcv5kBXA2eWUfigD7byekvf/JsOfKvPA==
  dependencies:
    "@vue/compiler-dom" "3.5.12"
    "@vue/shared" "3.5.12"

"@vue/devtools-api@^6.4.5", "@vue/devtools-api@^6.5.0":
  version "6.5.0"
  resolved "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.5.0.tgz"
  integrity sha512-o9KfBeaBmCKl10usN4crU53fYtC1r7jJwdGKjPT24t348rHxgfpZ0xL3Xm/gLUYnc0oTp8LAmrxOeLyu6tbk2Q==

"@vue/reactivity@^3.2.47", "@vue/reactivity@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.12.tgz"
  integrity sha512-UzaN3Da7xnJXdz4Okb/BGbAaomRHc3RdoWqTzlvd9+WBR5m3J39J1fGcHes7U3za0ruYn/iYy/a1euhMEHvTAg==
  dependencies:
    "@vue/shared" "3.5.12"

"@vue/runtime-core@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.12.tgz"
  integrity sha512-hrMUYV6tpocr3TL3Ad8DqxOdpDe4zuQY4HPY3X/VRh+L2myQO8MFXPAMarIOSGNu0bFAjh1yBkMPXZBqCk62Uw==
  dependencies:
    "@vue/reactivity" "3.5.12"
    "@vue/shared" "3.5.12"

"@vue/runtime-dom@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.12.tgz"
  integrity sha512-q8VFxR9A2MRfBr6/55Q3umyoN7ya836FzRXajPB6/Vvuv0zOPL+qltd9rIMzG/DbRLAIlREmnLsplEF/kotXKA==
  dependencies:
    "@vue/reactivity" "3.5.12"
    "@vue/runtime-core" "3.5.12"
    "@vue/shared" "3.5.12"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.12.tgz"
  integrity sha512-I3QoeDDeEPZm8yR28JtY+rk880Oqmj43hreIBVTicisFTx/Dl7JpG72g/X7YF8hnQD3IFhkky5i2bPonwrTVPg==
  dependencies:
    "@vue/compiler-ssr" "3.5.12"
    "@vue/shared" "3.5.12"

"@vue/shared@^3.2.47", "@vue/shared@3.5.12":
  version "3.5.12"
  resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.5.12.tgz"
  integrity sha512-L2RPSAwUFbgZH20etwrXyVyCBu9OxRSi8T/38QsvnkJyvq2LufW2lDCOzm7t/U9C1mkhJGWYfCuFBCmIuNivrg==

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
  integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

autoprefixer@^10.4.14:
  version "10.4.14"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.14.tgz"
  integrity sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ==
  dependencies:
    browserslist "^4.21.5"
    caniuse-lite "^1.0.30001464"
    fraction.js "^4.2.0"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

axios@^1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/axios/-/axios-1.7.2.tgz"
  integrity sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

blurhash@2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/blurhash/-/blurhash-2.0.5.tgz"
  integrity sha512-cRygWd7kGBQO3VEhPiTgq4Wc43ctsM+o46urrmPOiuAe+07fzlSB9OJVdpgDL0jPqXUVQ9ht7aq7kxOeJHRK+w==

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.21.5, "browserslist@>= 4.21.0":
  version "4.21.5"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz"
  integrity sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==
  dependencies:
    caniuse-lite "^1.0.30001449"
    electron-to-chromium "^1.4.284"
    node-releases "^2.0.8"
    update-browserslist-db "^1.0.10"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/call-bound/-/call-bound-1.0.3.tgz"
  integrity sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

caniuse-lite@^1.0.30001449, caniuse-lite@^1.0.30001464:
  version "1.0.30001624"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001624.tgz"
  integrity sha512-0dWnQG87UevOCPYaOR49CBcLBwoZLpws+k6W37nLjWUhumP1Isusj0p2u+3KhjNloRWK9OKMgjBBzPujQHw4nA==

chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

"ckeditor5@>=42.0.0 || ^0.0.0-nightly", ckeditor5@44.2.1:
  version "44.2.1"
  resolved "https://registry.npmjs.org/ckeditor5/-/ckeditor5-44.2.1.tgz"
  integrity sha512-pckC3jrAhtfFGnYgtihbiZ2yDeNT1lfXizOC7PikHfg4hqzXZnXI+XnvhlDeQcoHoRfMi/XJdUBHGbNicmJu3w==
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "44.2.1"
    "@ckeditor/ckeditor5-alignment" "44.2.1"
    "@ckeditor/ckeditor5-autoformat" "44.2.1"
    "@ckeditor/ckeditor5-autosave" "44.2.1"
    "@ckeditor/ckeditor5-basic-styles" "44.2.1"
    "@ckeditor/ckeditor5-block-quote" "44.2.1"
    "@ckeditor/ckeditor5-bookmark" "44.2.1"
    "@ckeditor/ckeditor5-ckbox" "44.2.1"
    "@ckeditor/ckeditor5-ckfinder" "44.2.1"
    "@ckeditor/ckeditor5-clipboard" "44.2.1"
    "@ckeditor/ckeditor5-cloud-services" "44.2.1"
    "@ckeditor/ckeditor5-code-block" "44.2.1"
    "@ckeditor/ckeditor5-core" "44.2.1"
    "@ckeditor/ckeditor5-easy-image" "44.2.1"
    "@ckeditor/ckeditor5-editor-balloon" "44.2.1"
    "@ckeditor/ckeditor5-editor-classic" "44.2.1"
    "@ckeditor/ckeditor5-editor-decoupled" "44.2.1"
    "@ckeditor/ckeditor5-editor-inline" "44.2.1"
    "@ckeditor/ckeditor5-editor-multi-root" "44.2.1"
    "@ckeditor/ckeditor5-emoji" "44.2.1"
    "@ckeditor/ckeditor5-engine" "44.2.1"
    "@ckeditor/ckeditor5-enter" "44.2.1"
    "@ckeditor/ckeditor5-essentials" "44.2.1"
    "@ckeditor/ckeditor5-find-and-replace" "44.2.1"
    "@ckeditor/ckeditor5-font" "44.2.1"
    "@ckeditor/ckeditor5-heading" "44.2.1"
    "@ckeditor/ckeditor5-highlight" "44.2.1"
    "@ckeditor/ckeditor5-horizontal-line" "44.2.1"
    "@ckeditor/ckeditor5-html-embed" "44.2.1"
    "@ckeditor/ckeditor5-html-support" "44.2.1"
    "@ckeditor/ckeditor5-image" "44.2.1"
    "@ckeditor/ckeditor5-indent" "44.2.1"
    "@ckeditor/ckeditor5-language" "44.2.1"
    "@ckeditor/ckeditor5-link" "44.2.1"
    "@ckeditor/ckeditor5-list" "44.2.1"
    "@ckeditor/ckeditor5-markdown-gfm" "44.2.1"
    "@ckeditor/ckeditor5-media-embed" "44.2.1"
    "@ckeditor/ckeditor5-mention" "44.2.1"
    "@ckeditor/ckeditor5-minimap" "44.2.1"
    "@ckeditor/ckeditor5-page-break" "44.2.1"
    "@ckeditor/ckeditor5-paragraph" "44.2.1"
    "@ckeditor/ckeditor5-paste-from-office" "44.2.1"
    "@ckeditor/ckeditor5-remove-format" "44.2.1"
    "@ckeditor/ckeditor5-restricted-editing" "44.2.1"
    "@ckeditor/ckeditor5-select-all" "44.2.1"
    "@ckeditor/ckeditor5-show-blocks" "44.2.1"
    "@ckeditor/ckeditor5-source-editing" "44.2.1"
    "@ckeditor/ckeditor5-special-characters" "44.2.1"
    "@ckeditor/ckeditor5-style" "44.2.1"
    "@ckeditor/ckeditor5-table" "44.2.1"
    "@ckeditor/ckeditor5-theme-lark" "44.2.1"
    "@ckeditor/ckeditor5-typing" "44.2.1"
    "@ckeditor/ckeditor5-ui" "44.2.1"
    "@ckeditor/ckeditor5-undo" "44.2.1"
    "@ckeditor/ckeditor5-upload" "44.2.1"
    "@ckeditor/ckeditor5-utils" "44.2.1"
    "@ckeditor/ckeditor5-watchdog" "44.2.1"
    "@ckeditor/ckeditor5-widget" "44.2.1"
    "@ckeditor/ckeditor5-word-count" "44.2.1"

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

color-convert@^2.0.1, color-convert@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-parse@1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/color-parse/-/color-parse-1.4.2.tgz"
  integrity sha512-RI7s49/8yqDj3fECFZjUI1Yi0z/Gq1py43oNJivAIIDSyJiOZLfYCRQEgn8HEVAj++PcRe8AnL2XF0fRJ3BTnA==
  dependencies:
    color-name "^1.0.0"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

cross-spawn@^7.0.0:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

csstype@^3.1.0, csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

dayjs@^1.11.7:
  version "1.11.7"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.7.tgz"
  integrity sha512-+Yw9U6YO5TQohxLcIkrXBeY73WP3ejHWVvx8XCk3gxvQDCTEmS48ZrSZCKciI7Bhl/uCMyxYtE9UqRILmFphkQ==

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz"
  integrity sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==

deep-equal@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz"
  integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

electron-to-chromium@^1.4.284:
  version "1.4.356"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.356.tgz"
  integrity sha512-nEftV1dRX3omlxAj42FwqRZT0i4xd2dIg39sog/CnCJeCcL1TRd2Uh0i9Oebgv8Ou0vzTPw++xc+Z20jzS2B6A==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

entities@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

esbuild@^0.18.10:
  version "0.18.20"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz"
  integrity sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==
  optionalDependencies:
    "@esbuild/android-arm" "0.18.20"
    "@esbuild/android-arm64" "0.18.20"
    "@esbuild/android-x64" "0.18.20"
    "@esbuild/darwin-arm64" "0.18.20"
    "@esbuild/darwin-x64" "0.18.20"
    "@esbuild/freebsd-arm64" "0.18.20"
    "@esbuild/freebsd-x64" "0.18.20"
    "@esbuild/linux-arm" "0.18.20"
    "@esbuild/linux-arm64" "0.18.20"
    "@esbuild/linux-ia32" "0.18.20"
    "@esbuild/linux-loong64" "0.18.20"
    "@esbuild/linux-mips64el" "0.18.20"
    "@esbuild/linux-ppc64" "0.18.20"
    "@esbuild/linux-riscv64" "0.18.20"
    "@esbuild/linux-s390x" "0.18.20"
    "@esbuild/linux-x64" "0.18.20"
    "@esbuild/netbsd-x64" "0.18.20"
    "@esbuild/openbsd-x64" "0.18.20"
    "@esbuild/sunos-x64" "0.18.20"
    "@esbuild/win32-arm64" "0.18.20"
    "@esbuild/win32-ia32" "0.18.20"
    "@esbuild/win32-x64" "0.18.20"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz"
  integrity sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==

extend@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

fast-diff@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz"
  integrity sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==

fast-glob@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

fix@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/fix/-/fix-0.0.6.tgz"
  integrity sha512-UQ+8m0GnIakgpY+92a9y+pYoX3Y6eaW7WNTkPolQ7r58Fjzq7NhyRLMrZ6J6U1u4y7H7APugjRmZ+i6CAn4+Dg==
  dependencies:
    pipe "0.0.2"
    underscore "1.1.6"
    underscore.string "1.1.4"

flag-icons@^7.2.3:
  version "7.2.3"
  resolved "https://registry.npmjs.org/flag-icons/-/flag-icons-7.2.3.tgz"
  integrity sha512-X2gUdteNuqdNqob2KKTJTS+ZCvyWeLCtDz9Ty8uJP17Y4o82Y+U/Vd4JNrdwTAjagYsRznOn9DZ+E/Q52qbmqg==

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

foreground-child@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.1.1.tgz"
  integrity sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fraction.js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.2.0.tgz"
  integrity sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

fuzzysort@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/fuzzysort/-/fuzzysort-3.1.0.tgz"
  integrity sha512-sR9BNCjBg6LNgwvxlBd0sBABvQitkLzoVY9MYYROQVX/FvfJ4Mai9LsGhDgd8qYdds0bY77VzYd5iuB+v5rwQQ==

get-intrinsic@^1.2.4, get-intrinsic@^1.2.6:
  version "1.2.7"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.7.tgz"
  integrity sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob@^10.3.10:
  version "10.4.1"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.1.tgz"
  integrity sha512-2jelhlq3E4ho74ZyVLN03oKdAZVUa6UDZzFLVH1H7dnoax+y9qyaq8zBkfDIggjniU19z0wU18y16jMB2eyVIw==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    path-scurry "^1.11.1"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  integrity sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==

is-arguments@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz"
  integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-date-object@^1.0.5:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz"
  integrity sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-regex@^1.1.4:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

jackspeak@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.1.2.tgz"
  integrity sha512-kWmLKn2tRtfYMF/BakihVVRzBKOxz4gJMiL2Rj91WnAB5TPZumSH99R/Yf1qE1u4uRimvCSJfm6hnxohXeEXjQ==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz"
  integrity sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==

ldrs@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/ldrs/-/ldrs-1.0.2.tgz"
  integrity sha512-sYJmivdkIiHrUEqTrEWccBoLdaENpzbzkABI5rk8rRxTXrg9i2xVuDvUUuhOhJY3RmQyaoxs046pM1DCRdcIpg==

libphonenumber-js@^1.10.51:
  version "1.11.17"
  resolved "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.11.17.tgz"
  integrity sha512-Jr6v8thd5qRlOlc6CslSTzGzzQW03uiscab7KHQZX1Dfo4R6n6FDhZ0Hri6/X7edLIDv9gl4VMZXhxTjLnl0VQ==

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

lodash-es@^4.17.21, lodash-es@4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.castarray@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/lodash.castarray/-/lodash.castarray-4.4.0.tgz"
  integrity sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lru-cache@^10.2.0:
  version "10.2.2"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.2.tgz"
  integrity sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ==

magic-string@^0.30.11:
  version "0.30.12"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.12.tgz"
  integrity sha512-Ea8I3sQMVXr8JhN4z+H/d8zwo+tYDgHE9+5G4Wnrwhs0gaK9fXTKx0Tw5Xwsd/bCPTTZNRAdpyzvoeORe9LYpw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

marked@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/marked/-/marked-4.3.0.tgz"
  integrity sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A==

marked@4.0.12:
  version "4.0.12"
  resolved "https://registry.npmjs.org/marked/-/marked-4.0.12.tgz"
  integrity sha512-hgibXWrEDNBWgGiK18j/4lkS6ihTe9sxtV4Q1OQppb/0zzyPSzoFANBa5MfsG/zgsWklmNnhm0XACZOH/0HBiQ==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mini-svg-data-uri@^1.2.3:
  version "1.4.4"
  resolved "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz"
  integrity sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==

minimatch@^6.1.6:
  version "6.2.0"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-6.2.0.tgz"
  integrity sha512-sauLxniAmvnhhRjFwPNnJKaPFYyddAgbYdeUpHULtCT/GhzdCx/MDNy+Y40lBxTQUrMzDE8e0S43Z5uqfO0REg==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.4.tgz"
  integrity sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==
  dependencies:
    brace-expansion "^2.0.1"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

muggle-string@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/muggle-string/-/muggle-string-0.2.2.tgz"
  integrity sha512-YVE1mIJ4VpUMqZObFndk9CJu6DBJR/GB13p3tXuNbwD4XExaI5EOuRl6BHeIDxIqXZVxSfAC+y6U1Z/IxCfKUg==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

node-releases@^2.0.8:
  version "2.0.10"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.10.tgz"
  integrity sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

parchment@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz"
  integrity sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

pathe@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/pathe/-/pathe-1.1.2.tgz"
  integrity sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==

perfect-scrollbar@^1.5.5:
  version "1.5.5"
  resolved "https://registry.npmjs.org/perfect-scrollbar/-/perfect-scrollbar-1.5.5.tgz"
  integrity sha512-dzalfutyP3e/FOpdlhVryN4AJ5XDVauVWxybSkLZmakFE2sS3y3pc4JnSprw8tGmHvkaG5Edr5T7LBTZ+WWU2g==

picocolors@^1.0.0, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pinia@^2.0.35:
  version "2.0.35"
  resolved "https://registry.npmjs.org/pinia/-/pinia-2.0.35.tgz"
  integrity sha512-P1IKKQWhxGXiiZ3atOaNI75bYlFUbRxtJdhPLX059Z7+b9Z04rnTZdSY8Aph1LA+/4QEMAYHsTQ638Wfe+6K5g==
  dependencies:
    "@vue/devtools-api" "^6.5.0"
    vue-demi "*"

pipe@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/pipe/-/pipe-0.0.2.tgz"
  integrity sha512-67s0/X7rv2PX1sl64FQqC0qQuSpd1tv8Wh6c+U1lprj6Q7NxDYulCxZTbVbDvc/HSpZLYh7Oo821xReXSCZikQ==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-selector-parser@6.0.10:
  version "6.0.10"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  integrity sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.0.0, postcss@^8.1.0, postcss@^8.2.14, postcss@^8.4.14, postcss@^8.4.21, postcss@^8.4.27, postcss@^8.4.47, postcss@>=8.0.9:
  version "8.4.47"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.47.tgz"
  integrity sha512-56rxCq7G/XfB4EkXq9Egn5GCqugWvDFjafDOThIdMBsI15iqPqR5r15TfSr1YPYeEI19YeaXMCbY6u88Y76GLQ==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.0"
    source-map-js "^1.2.1"

preact@^10.19.2, preact@^10.24.3, preact@10.x:
  version "10.24.3"
  resolved "https://registry.npmjs.org/preact/-/preact-10.24.3.tgz"
  integrity sha512-Z2dPnBnMUfyQfSQ+GBdsGa16hz35YmLmtTLhM169uW944hYL6xzTYkJjC07j+Wosz733pMWx0fgON3JNw1jJQA==

"prettier@^1.18.2 || ^2.0.0":
  version "2.8.8"
  resolved "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

primeicons@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/primeicons/-/primeicons-7.0.0.tgz"
  integrity sha512-jK3Et9UzwzTsd6tzl2RmwrVY/b8raJ3QZLzoDACj+oTJ0oX7L9Hy+XnVwgo4QVKlKpnP/Ur13SXV/pVh4LzaDw==

primevue@^3.52.0:
  version "3.52.0"
  resolved "https://registry.npmjs.org/primevue/-/primevue-3.52.0.tgz"
  integrity sha512-HLOVP5YI0ArFKUhIyfZsWmTNMaBYNCBWC/3DYvdd/Po4LY5/WXf7yIYvArE2q/3OuwSXJXvjlR8UNQeJYRSQog==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

qalendar@^3.8.1:
  version "3.8.1"
  resolved "https://registry.npmjs.org/qalendar/-/qalendar-3.8.1.tgz"
  integrity sha512-RTIbPzlw/xQj2D6JEho/ac8Bnl/x3aXM8D1P0UQ2blFYT+bAcmxrs43btAfhBZqikbDdUK+FiMDC1cgqas1lpA==
  dependencies:
    "@fortawesome/fontawesome-svg-core" "^6.3.0"
    "@fortawesome/free-regular-svg-icons" "^6.3.0"
    "@fortawesome/free-solid-svg-icons" "^6.3.0"
    "@fortawesome/vue-fontawesome" "^3.0.3"
    perfect-scrollbar "^1.5.5"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz"
  integrity sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill@^1.3.4:
  version "1.3.7"
  resolved "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz"
  integrity sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

regexp.prototype.flags@^1.5.1:
  version "1.5.4"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

resolve@^1.1.7, resolve@^1.22.8:
  version "1.22.10"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rollup@^3.27.1:
  version "3.29.4"
  resolved "https://registry.npmjs.org/rollup/-/rollup-3.29.4.tgz"
  integrity sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

tailwindcss@^3.4.17, "tailwindcss@>=3.0.0 || >= 3.0.0-alpha.1", "tailwindcss@>=3.0.0 || insiders":
  version "3.4.17"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz"
  integrity sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

turndown-plugin-gfm@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/turndown-plugin-gfm/-/turndown-plugin-gfm-1.0.2.tgz"
  integrity sha512-vwz9tfvF7XN/jE0dGoBei3FXWuvll78ohzCZQuOb+ZjWrs3a0XhQVomJEb2Qh4VHTPNRO4GPZh0V7VRbiWwkRg==

turndown@7.2.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/turndown/-/turndown-7.2.0.tgz"
  integrity sha512-eCZGBN4nNNqM9Owkv9HAtWRYfLA4h909E/WGAWWBpmB275ehNhZyk87/Tpvjbp0jjNl9XwCsbe6bm6CqFsgD+A==
  dependencies:
    "@mixmark-io/domino" "^2.2.0"

typescript@*, typescript@^4.6.4, typescript@>=4.4.4, typescript@>=4.7:
  version "4.9.5"
  resolved "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

underscore.string@1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/underscore.string/-/underscore.string-1.1.4.tgz"
  integrity sha512-WsF8NWzIbTvxUaSOpSLq+AiO0tzweXdWQZ4w9Op8S/1BT9Fh7hCS7bfrF17vZu9kJg3pcqO+8WXfQSr1ah0f2g==
  dependencies:
    underscore "1.1.6"

underscore@1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/underscore/-/underscore-1.1.6.tgz"
  integrity sha512-aqSzrO92Cjmeo8G7F49+ZHWBo3IJpjpsUZZaqfOHJGN61flbpLxQw/sP91p4kf/2+nkFrG6AG2WHlJh6RCf+/g==

update-browserslist-db@^1.0.10:
  version "1.0.10"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  integrity sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.0.tgz"
  integrity sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==

vanilla-colorful@0.7.2:
  version "0.7.2"
  resolved "https://registry.npmjs.org/vanilla-colorful/-/vanilla-colorful-0.7.2.tgz"
  integrity sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg==

vite@^4.0.0:
  version "4.5.3"
  resolved "https://registry.npmjs.org/vite/-/vite-4.5.3.tgz"
  integrity sha512-kQL23kMeX92v3ph7IauVkXkikdDRsYMGTVl5KY2E9OY4ONLvkHf04MDTbnfo6NKxZiDLWzVpP5oTa8hQD8U3dg==
  dependencies:
    esbuild "^0.18.10"
    postcss "^8.4.27"
    rollup "^3.27.1"
  optionalDependencies:
    fsevents "~2.3.2"

vue-demi@*:
  version "0.14.0"
  resolved "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.0.tgz"
  integrity sha512-gt58r2ogsNQeVoQ3EhoUAvUsH9xviydl0dWJj7dabBC/2L4uBId7ujtCwDRD0JhkGsV1i0CtfLAeyYKBht9oWg==

vue-quill-editor@^3.0.6:
  version "3.0.6"
  resolved "https://registry.npmjs.org/vue-quill-editor/-/vue-quill-editor-3.0.6.tgz"
  integrity sha512-g20oSZNWg8Hbu41Kinjd55e235qVWPLfg4NvsLW6d+DhgBTFbEuMpcWlUdrD6qT3+Noim6DRu18VLM9lVShXOQ==
  dependencies:
    object-assign "^4.1.1"
    quill "^1.3.4"

vue-router@^4.1.6:
  version "4.1.6"
  resolved "https://registry.npmjs.org/vue-router/-/vue-router-4.1.6.tgz"
  integrity sha512-DYWYwsG6xNPmLq/FmZn8Ip+qrhFEzA14EI12MsMgVxvHFDYvlr4NXpVF5hrRH1wVcDP8fGi5F4rxuJSl8/r+EQ==
  dependencies:
    "@vue/devtools-api" "^6.4.5"

vue-sonner@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/vue-sonner/-/vue-sonner-1.3.0.tgz"
  integrity sha512-jAodBy4Mri8rQjVZGQAPs4ZYymc1ywPiwfa81qU0fFl+Suk7U8NaOxIDdI1oBGLeQJqRZi/oxNIuhCLqsBmOwg==

vue-tel-input@^9.2.0:
  version "9.3.0"
  resolved "https://registry.npmjs.org/vue-tel-input/-/vue-tel-input-9.3.0.tgz"
  integrity sha512-8PgAFxO5npztMruL1O0NZxPDJScfc9Qx2mDERErRKS7XQ1l/MSk8rfi0XgPbNdOBiocdweDnikNdo3xSJrQodA==

vue-template-compiler@^2.7.14:
  version "2.7.14"
  resolved "https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.7.14.tgz"
  integrity sha512-zyA5Y3ArvVG0NacJDkkzJuPQDF8RFeRlzV2vLeSnhSpieO6LK2OVbdLPi5MPPs09Ii+gMO8nY4S3iKQxBxDmWQ==
  dependencies:
    de-indent "^1.0.2"
    he "^1.2.0"

vue-tsc@^1.0.11:
  version "1.2.0"
  resolved "https://registry.npmjs.org/vue-tsc/-/vue-tsc-1.2.0.tgz"
  integrity sha512-rIlzqdrhyPYyLG9zxsVRa+JEseeS9s8F2BbVVVWRRsTZvJO2BbhLEb2HW3MY+DFma0378tnIqs+vfTzbcQtRFw==
  dependencies:
    "@volar/vue-language-core" "1.2.0"
    "@volar/vue-typescript" "1.2.0"

vue@^2.0.0:
  version "2.7.16"
  resolved "https://registry.npmjs.org/vue/-/vue-2.7.16.tgz"
  integrity sha512-4gCtFXaAA3zYZdTp5s4Hl2sozuySsgz4jy1EnpBHNfpMa9dK1ZCG7viqBPCwXtmgc8nHqUsAu3G4gtmXkkY3Sw==
  dependencies:
    "@vue/compiler-sfc" "2.7.16"
    csstype "^3.1.0"

"vue@^2.6.14 || ^3.2.0", "vue@^2.7.0 || ^3.0.0", vue@^3, vue@^3.0.0, "vue@^3.0.0-0 || ^2.6.0", vue@^3.0.3, vue@^3.2.0, vue@^3.2.25, vue@^3.2.45, vue@^3.3.0, vue@^3.4.0, vue@^3.5.4, "vue@>= 3", "vue@>= 3.0.0 < 4", vue@>=3, vue@3.5.12:
  version "3.5.12"
  resolved "https://registry.npmjs.org/vue/-/vue-3.5.12.tgz"
  integrity sha512-CLVZtXtn2ItBIi/zHZ0Sg1Xkb7+PU32bJJ8Bmy7ts3jxXTcbfsEfBivFYYWz1Hur+lalqGAh65Coin0r+HRUfg==
  dependencies:
    "@vue/compiler-dom" "3.5.12"
    "@vue/compiler-sfc" "3.5.12"
    "@vue/runtime-dom" "3.5.12"
    "@vue/server-renderer" "3.5.12"
    "@vue/shared" "3.5.12"

vue3-google-login@^2.0.26:
  version "2.0.26"
  resolved "https://registry.npmjs.org/vue3-google-login/-/vue3-google-login-2.0.26.tgz"
  integrity sha512-BuTSIeSjINNHNPs+BDF4COnjWvff27IfCBDxK6JPRqvm57lF8iK4B3+zcG8ud6BXfZdyuiDlxletbEDgg4/RFA==

vuetify@^3.7.3:
  version "3.7.3"
  resolved "https://registry.npmjs.org/vuetify/-/vuetify-3.7.3.tgz"
  integrity sha512-bpuvBpZl1/+nLlXDgdVXekvMNR6W/ciaoa8CYlpeAzAARbY8zUFSoBq05JlLhkIHI58AnzKVy4c09d0OtfYAPg==

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

yaml@^2.3.4:
  version "2.4.3"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.4.3.tgz"
  integrity sha512-sntgmxj8o7DE7g/Qi60cqpLBA3HG3STcDA0kO+WfB05jEKhZMbY7umNm2rBpQvsmZ16/lPXCJGW2672dgOUkrg==

zod@^3.24.1:
  version "3.24.1"
  resolved "https://registry.npmjs.org/zod/-/zod-3.24.1.tgz"
  integrity sha512-muH7gBL9sI1nciMZV67X5fTKKBLtwpZ5VBp1vsOQzj1MhrBZ4wlVCm3gedKZWLp0Oyel8sIGfeiz54Su+OVT+A==
