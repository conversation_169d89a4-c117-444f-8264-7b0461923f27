<template>
  <div data-tauri-drag-region class="absolutex z-50 inset-x-0 flex justify-between items-center px-2 h-8 bg-primary">
    <div class="flex gap-2 w-16" data-tauri-drag-region>
      <button @click="appWindow.close()" class="p-[1px] bg-red-400 rounded-full" id="titlebar-close">
        <XMarkIcon class="w-3 h-3 transition duration-75 ease-in-out opacity-0 hover:opacity-100" />
      </button>
      <button @click="appWindow.minimize()" class="p-[1px] bg-yellow-400 rounded-full" id="titlebar-close">
        <MinusSmallIcon class="w-3 h-3 transition duration-75 ease-in-out opacity-0 hover:opacity-100" />
      </button>
      <button @click="appWindow.maximize()" class="p-[1px] bg-green-500 rounded-full" id="titlebar-close">
        <PlusSmallIcon class="w-3 h-3 transition duration-75 ease-in-out opacity-0 hover:opacity-100" />
      </button>
    </div>
    <div class="w-[calc(100%-128px)] flex justify-center items-center" data-tauri-drag-region>
      <SearchInput v-if="route.path === '/emails'" @change="search" />
    </div>
    <div class="flex gap-2 w-16" data-tauri-drag-region></div>
  </div>
</template>

<script setup lang="ts">
import { MinusSmallIcon, PlusSmallIcon, XMarkIcon } from "@heroicons/vue/20/solid";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import SearchInput from "./ui/SearchInput.vue";
import { useRoute, useRouter } from "vue-router";
import { parseSearchQuery, SearchQuery } from "../utils/search-query";
import { useEmailSearchStore } from "../stores/searchEmailStore";
const appWindow = getCurrentWebviewWindow();

const route = useRoute();
const searchQueryStore = useEmailSearchStore();

function search(value: string) {
  const query: SearchQuery = parseSearchQuery(value);
  //  console.log("Search Query =>", query);
  //  console.log("Search ", value);
  searchQueryStore.setSearchQuery(query);
}
</script>
