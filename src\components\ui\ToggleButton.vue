<template>
  <label :for="props.id">
    <div class="bg-primary-300 w-10 h-5 rounded-full cursor-pointer">
      <input type="checkbox" :id="props.id" class="hidden peer" v-model="currentValue" />
      <div class="size-5 bg-primary-700 rounded-full peer-checked:translate-x-full transition-all duration-200"></div>
    </div>
  </label>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  value: {
    type: Boolean,
  },
  // modelValue: {
  //   type: <PERSON><PERSON><PERSON>,
  // },
});

const currentValue = ref<boolean>(props.value);
const emit = defineEmits<{
  (e: "changed", value: boolean): void;
}>();
// const emit = defineEmits(["changed", "update:modelValue"]);

watch(currentValue, (value) => {
  emit("changed", value);
  // emit("update:modelValue", value);
});
</script>

<style scoped></style>
