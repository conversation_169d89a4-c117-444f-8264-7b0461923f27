<template>
  <section class="w-full h-auto flex flex-col gap-1">
    <div class="flex justify-between items-center">
      <div class="text-sm text-secondary-500 font-semibold">{{ props.title }}</div>
      <div v-if="props.editable" class="flex gap-1">
        <button
          class="text-primary-800 px-2 rounded text-xs h-6 flex justify-center items-center"
          @click="emit('onEdit')"
          v-if="!props.editing"
        >
          <Icon v-if="!props.editing" icon="basil:edit-outline" class="size-4" />
        </button>
        <button
          class="bg-primary-600 px-2 rounded text-xs h-6 flex justify-center items-center text-white"
          @click="emit('onSave')"
          v-if="props.editing"
        >
          <p>Save</p>
        </button>
        <button
          class="text-primary-800 px-2 rounded text-xs h-6 flex justify-center items-center underline"
          @click="emit('onCancel')"
          v-if="props.editing"
        >
          <p>Cancel</p>
        </button>
      </div>
    </div>
    <div class="border border-primary-600 p-1 rounded bg-white/80"><slot></slot></div>
  </section>
</template>

<script setup lang="ts">
import { Icon } from "@iconify/vue/dist/iconify.js";

const props = defineProps({
  title: String,
  editable: {
    type: Boolean,
    default: false,
  },
  editing: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: "onEdit"): void;
  (e: "onSave"): Promise<void>;
  (e: "onCancel"): void;
}>();
</script>

<style scoped></style>
