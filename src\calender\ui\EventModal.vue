<template>
  <div class="size-full flex justify-between items-center pb-2">
    <div class="w-1/4 h-full py-3 bg-primary border-r-2 border-[#e1e1e1]">
      <div class="flex justify-between items-center px-1">
        <button
          @click="() => emit('close')"
          class="size-8 flex justify-start items-center gap-1 text-primary-500 hover:text-primary-600 transition-all duration-200"
        >
          <ChevronLeftIcon class="size-6" />
        </button>
        <div class="text-sm font-semibold text-secondary-700">Events Management</div>
        <div
          class="size-6 flex justify-center items-center rounded-md bg-primary-200 text-primary-700 hover:text-primary-900 cursor-pointer transition-all duration-300"
          @click="
            selEvent = null;
            editableSch = null;
          "
        >
          <PlusIcon class="size-5" />
        </div>
      </div>
      <div class="flex flex-col gap-2 w-full h-auto px-1 mt-4">
        <div
          class="group flex justify-between items-center px-1 rounded-md w-full h-14 hover:bg-primary-600/50 transition-all duration-300 cursor-pointer"
          v-for="event in events"
          :key="event.id"
          :class="editableSch && editableSch.id == event.id ? 'bg-primary-300 text-primary-800' : 'bg-primary-600/30'"
          @click="() => selectSchedule(event)"
        >
          <div class="flex flex-col gap-1">
            <div class="flex justify-start items-center flex-wrap">
              <div class="text-nowrapx truncate max-w-36 mr-2">{{ event.title }}</div>
              <div v-if="event.hidden" class="py-0.5 px-1 rounded text-xs bg-primary-700 text-white">Hidden</div>
            </div>
          </div>

          <div class="group-hover:flex hidden h-full items-center w-fit gap-1">
            <button
              v-if="event.id !== undefined"
              @click="() => (event.id ? openEventLink(event.slug) : undefined)"
              class="bg-primary-600 p-1 rounded-lg text-dark hover:bg-primary-400 transition-all duration-300"
            >
              <PaperClipIcon class="size-4" />
            </button>
            <button
              @click="async () => (event.link ? await copyLink(event.link) : undefined)"
              class="bg-primary-600 p-1 rounded-lg text-dark hover:bg-primary-400 transition-all duration-300"
            >
              <CheckIcon v-if="event.link == copiedLink" class="size-4" />
              <LinkIcon v-else class="size-4" />
            </button>

            <button
              @click="async () => (event.link ? await open(event.link) : undefined)"
              class="bg-primary-600 p-1 rounded-lg text-dark hover:bg-primary-400 transition-all duration-300"
            >
              <EyeIcon class="size-4" />
            </button>
            <button
              @click="() => {}"
              class="bg-red-200 p-1 rounded-lg text-red-600 hover:bg-red-300 transition-all duration-300"
            >
              <TrashIcon class="size-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
    <div :key="selEvent?.id" class="w-3/4 h-full p-2 flex flex-col gap-2 overflow-y-auto custom-scrollbar">
      <EventTypeForm :data="editableSch?.id ? editableSch : defaultEventType" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { useCurrentUserStore } from "../../stores/currentUser";
import {
  PaperClipIcon,
  CheckIcon,
  ChevronLeftIcon,
  EyeIcon,
  GlobeAltIcon,
  LinkIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/vue/24/outline";

import { EventType } from "../../models/event-type-model";
import { calEventTypeService } from "../../services/event-type-cal-service";
import { open } from "@tauri-apps/plugin-shell";
import { writeText } from "@tauri-apps/plugin-clipboard-manager";
import EventTypeForm from "../forms/event-type/EventTypeForm.vue";
import { defaultEventType } from "../../models/event-type-model";

const emit = defineEmits(["close"]);

const copiedLink = ref<string | null>(null);

const events = ref<EventType[]>([]);
const selEvent = ref<EventType | null>(null);
const session = useCurrentUserStore();
const editableSch = ref<EventType | null>(null);

async function getEventTypes() {
  if (session.calInfo?.id) events.value = (await calEventTypeService.getUserEventType(session.calInfo?.id)) ?? [];
}

function selectSchedule(s: EventType) {
  selEvent.value = s;
  editableSch.value = { ...s };
}

async function copyLink(link: string) {
  await writeText(link).then(() => {
    copiedLink.value = link;
    setTimeout(() => {
      copiedLink.value = null;
    }, 2000);
  });
}

async function openEventLink(slug: string) {
  //  console.log("Button clicked");

  //  console.log("Button clicked");
  const username = session.calInfo?.username;

  //  console.log(session.calInfo, slug);
  if (username && slug) {
    const url = `${import.meta.env.VITE_CAL_API_HOST}/${username}/${slug}`;
    //  console.log("Opening URL:", url);

    // Open the constructed URL in the system's default browser
    await open(url);
  } else {
    console.error("Username or Event ID is missing");
  }
}

watch(
  selEvent,
  (value) => {
    //  console.log("Selected event", value);
  },
  { deep: true }
);

onMounted(async () => {
  await getEventTypes();
});
</script>

<style scoped></style>
