

import CalApi from "./cal-api-service";
import { CalAvailability } from "../models/availability-model";
import { EventType } from "../models/event-type-model";

interface CalEventTypesRes {
  event_types: EventType[]
}

interface CalEventTypeRes {
  event_type: EventType
}

async function getUserEventType(userId: number): Promise<EventType[] | null> {
  try {

    const response = await CalApi.get<CalEventTypesRes>("api/event-types", { params: { userId } });
    //  console.log("CREATE USER =>", response.data);
    return response.data.event_types;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function updateEventType(etId: number, et: EventType): Promise<EventType | null> {
  try {
    const {
      id,
      userId,
      teamId,
      metadata,
      customInputs,
      link,
      hashedLink,

      ...updateFields
    } = et;
    const response = await CalApi.patch<CalEventTypeRes>(`api/event-types/${etId}`, updateFields);
    return response.data.event_type;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function removeAvailability(avId: number): Promise<CalAvailability | null> {
  try {
    const response = await CalApi.delete<CalAvailability>(`api/availabilities/${avId}`);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function createEventType(et: EventType): Promise<EventType | null> {
  try {
    //  console.log("EventType before posting:", et); // Log the entire `et` object
    et.length = Number(et.length);
    delete et.id;
    const response = await CalApi.post<CalEventTypeRes>(`api/event-types`, et);
    return response.data.event_type;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}






export const calEventTypeService = {
  getUserEventType,
  createEventType,
  updateEventType
}
