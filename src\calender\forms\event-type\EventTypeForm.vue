<template>
  <div class="size-full flex flex-col gap-2" :key="data.title">
    <div class="flex justify-between items-center">
      <div class="w-32"></div>
      <div class="flex gap-5">
        <div class="flex justify-between items-center gap-5 cursor-pointer" v-for="(step, index) of steps">
          <div @click="currentStep = step" class="flex justify-start items-center gap-3">
            <div
              :class="{
                'bg-primary-200 text-primary-800': currentStep != step,
                'bg-primary-500 text-primary-100': currentStep === step,
              }"
              class="size-6 font-semibold flex justify-center items-center rounded-full"
            >
              {{ index + 1 }}
            </div>
            <div
              :class="{
                ' text-primary-800/30': currentStep != step,
                ' text-primary-800': currentStep === step,
              }"
            >
              {{ step }}
            </div>
          </div>
          <div v-if="index != steps.length - 1">
            <ChevronRightIcon class="size-6" />
          </div>
        </div>
      </div>
      <div class="flex items-center gap-1 w-32 overflow-hidden">
        <button
          @click="() => (data.id == 0 ? create() : save())"
          class="px-4 py-1 bg-primary-500 rounded text-primary-100"
        >
          <l-ring v-if="isLoading" size="24" stroke="2" bg-opacity="0" speed="2" color="#f0f0f0"></l-ring>
          <div v-else>{{ data.id == 0 ? "Create" : "Save" }}</div>
        </button>
        <button @click="data = defaultEventType" class="bg-primary-200 text-primary-800 p-1.5 rounded-md">
          <ArrowPathIcon class="size-5" />
        </button>
      </div>
    </div>
    <div class="flex-1 p-2">
      <Setup v-show="currentStep === 'Setup'" :setup="data" @change="updateData" />
      <AvailablityStep
        v-show="currentStep === 'Availability'"
        :selected-id="data.scheduleId === 0 ? undefined : data.scheduleId"
        @change="(value:number)=>updateData({scheduleId:value})"
      />
      <LimitsStep v-show="currentStep === 'Limits'" :limits="data" @change="updateData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon, ChevronRightIcon } from "@heroicons/vue/24/outline";
import { onMounted, ref, watch } from "vue";
import Setup from "./steps/Setup.vue";
import AvailablityStep from "./steps/AvailablityStep.vue";
import LimitsStep from "./steps/LimitsStep.vue";
import { defaultEventType, EventType } from "../../../models/event-type-model";
import { calEventTypeService } from "../../../services/event-type-cal-service";
import { ring } from "ldrs";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { toast } from "vue-sonner";

ring.register();

type Steps = "Setup" | "Availability" | "Limits";
const steps: Steps[] = ["Setup", "Availability", "Limits"];
const currentStep = ref<Steps>("Setup");
const isLoading = ref(false);

const props = defineProps({
  data: {
    type: Object,
  },
});

const data = ref<EventType>((props.data as EventType) ?? defaultEventType);

function updateData(value: Partial<EventType>) {
  data.value = {
    ...data.value,
    ...value,
  };

  //  console.log("updated Data ===> ", data.value);
}

watch(
  data,
  (value) => {
    //  console.log("#### ACTUAL DATA=>", value);
  },

  { deep: true }
);

onMounted(() => {
  //  console.log("#### ACTUAL DATA   =>", data.value);
});
const session = useCurrentUserStore();

async function create() {
  //  console.log("############### CREATE EVENT ##############\n");
  //  console.table([data.value]);
  try {
    if (data.value && data.value.title && data.value.slug && session.calInfo?.id) {
      isLoading.value = true;
      data.value.userId = session.calInfo?.id;
      delete data.value.recurringEvent;
      // await calEventTypeService.createEventType(data.value);
      toast.promise(() => calEventTypeService.createEventType(data.value), {
        loading: "Creating...",
        success: () => {
          return `Event has been created!`;
        },
        error: () => "Couldn't create the event!",
      });
      isLoading.value = false;
    } else toast.error("Invalid event data!");
  } catch (error: any) {
    console.error(error);
    toast.error(error.message);
  }
}

async function save() {
  try {
    //  console.log("############### SAVE EVENT ##############\n");
    //  console.table([data.value]);
    //  console.log(`SAVE EVENT ${data.value.title} with is ${data.value.id}`);
    if (
      data.value &&
      data.value.id &&
      data.value.id != 0 &&
      session.calInfo?.id &&
      data.value.title &&
      data.value.slug
    ) {
      isLoading.value = true;
      data.value.userId = session.calInfo?.id;
      delete data.value.seatsPerTimeSlot;
      delete data.value.recurringEvent;
      // await calEventTypeService.updateEventType(data.value.id, data.value);
      toast.promise(() => calEventTypeService.updateEventType(data.value.id!, data.value), {
        loading: "Saving...",
        success: () => {
          return `Event has been saved!`;
        },
        error: () => "Couldn't save the event!",
      });
      isLoading.value = false;
    } else toast.error("Invalid event data!");
  } catch (error: any) {
    console.error("EventTypeForm:Save:Error", error);
    toast.error(error.message);
  }
}
</script>

<style scoped></style>
