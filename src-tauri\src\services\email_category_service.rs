use crate::{
    db::establish_db_connection,
    db::get_pooled_connection,
    models::email_category::{EmailCategory, NewEmailCategory},
    schema::email_categories,
    schema::email_categories::dsl,
    schema::emails::dsl as email_dsl,
};

use crate::HashSet;
use chrono::NaiveDate;
use diesel::dsl::exists;
use diesel::prelude::*;
use diesel::result::Error;
use diesel::select;
use diesel::Connection;
use std::thread;
use std::time::Duration;
use uuid::Uuid;

pub fn get_email_category(category_id: &String) -> Result<Option<EmailCategory>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        let result = dsl::email_categories
            .filter(dsl::category_id.eq(category_id))
            .filter(dsl::child_full_domains.ne("")) // Check where child_full_domains is an empty string
            .first::<EmailCategory>(conn)
            .optional()?;
        Ok(result)
    })
}

pub fn store_email_category(new_email_category: &NewEmailCategory) -> Result<(), Error> {
    let max_attempts = 5; // Maximum number of retry attempts
    let mut attempts = 0;
    let mut backoff = 100; // Initial backoff time in milliseconds

    while attempts < max_attempts {
        // let connection = &mut establish_db_connection();
        let connection = &mut get_pooled_connection();

        let result = connection.transaction::<_, Error, _>(|conn| {
            let now = chrono::Utc::now().naive_utc();
            let unread_increment = 1;

            // First: Does this category already exist as a child (with parent)?
            let existing_category = email_categories::dsl::email_categories
                .filter(email_categories::dsl::category_id.eq(&new_email_category.category_id))
                .filter(email_categories::dsl::parent_category_id.ne(""))
                .first::<EmailCategory>(conn)
                .optional()?;

            if let Some(_) = existing_category {
                println!("Existing child category found, updating parent...");

                diesel::update(
                    email_categories::dsl::email_categories
                        .filter(
                            email_categories::dsl::category_id
                                .eq(&new_email_category.sender_domain),
                        )
                        .filter(email_categories::dsl::child_full_domains.ne("")), // Parent
                )
                .set((
                    email_categories::dsl::latest_email_at.eq(now),
                    email_categories::dsl::unread_count
                        .eq(email_categories::dsl::unread_count + unread_increment),
                ))
                .execute(conn)?;
            } else {
                // Check for existing parent
                let parent = email_categories::dsl::email_categories
                    .filter(
                        email_categories::dsl::category_id.eq(&new_email_category.sender_domain),
                    )
                    .filter(email_categories::dsl::parent_category_id.eq(""))
                    .first::<EmailCategory>(conn)
                    .optional()?;

                if let Some(mut parent_cat) = parent {
                    println!("Parent category found. Inserting child.");

                    // Add child domain if not already listed
                    let mut domains = parent_cat
                        .child_full_domains
                        .unwrap_or_default()
                        .split(',')
                        .map(|s| s.to_string())
                        .collect::<Vec<_>>();

                    if !domains.contains(&new_email_category.category_id) {
                        domains.push(new_email_category.category_id.clone());
                    }

                    diesel::insert_into(email_categories::table)
                        .values(NewEmailCategory {
                            parent_category_id: parent_cat.category_id.clone(),
                            child_full_domains: "".to_string(),
                            ..new_email_category.clone()
                        })
                        .execute(conn)?;

                    diesel::update(
                        email_categories::dsl::email_categories
                            .filter(email_categories::dsl::category_id.eq(&parent_cat.category_id)),
                    )
                    .set((
                        email_categories::dsl::unread_count
                            .eq(email_categories::dsl::unread_count + unread_increment),
                        email_categories::dsl::latest_email_at.eq(now),
                        email_categories::dsl::child_full_domains.eq(domains.join(",")),
                    ))
                    .execute(conn)?;
                } else {
                    println!("No parent found. Creating new parent and child.");

                    let parent = NewEmailCategory {
                        category_id: new_email_category.sender_domain.clone(),
                        parent_category_id: "".to_string(),
                        child_full_domains: new_email_category.category_id.clone(),
                        ..new_email_category.clone()
                    };

                    let child = NewEmailCategory {
                        parent_category_id: new_email_category.sender_domain.clone(),
                        child_full_domains: "".to_string(),
                        id: Uuid::new_v4().to_string(),
                        ..new_email_category.clone()
                    };

                    diesel::insert_into(email_categories::table)
                        .values(&parent)
                        .execute(conn)?;
                    diesel::insert_into(email_categories::table)
                        .values(&child)
                        .execute(conn)?;
                }
            }

            Ok(())
        });
        // let result = connection.transaction::<_, Error, _>(|conn| {
        //     // Step 1: Try to find an existing category by `category_id`
        //     let existing_category = email_categories::dsl::email_categories
        //         .filter(email_categories::dsl::category_id.eq(&new_email_category.category_id))
        //         .filter(email_categories::dsl::parent_category_id.ne(""))
        //         .first::<EmailCategory>(conn)
        //         .optional()?;

        //     if let Some(mut category) = existing_category {
        //         println!("Existing category found:");

        //         diesel::update(
        //             email_categories::dsl::email_categories.filter(
        //                 email_categories::dsl::category_id.eq(&new_email_category.category_id),
        //             ).filter(email_categories::dsl::child_full_domains.eq("")) // Ensure it's a primary category
        //             .filter(email_categories::dsl::parent_category_id.ne("")), // .filter(email_categories::dsl::child_full_domains.eq("")), // Ensure it's a primary category
        //         )
        //         .set((
        //             email_categories::dsl::latest_email_at.eq(chrono::Utc::now().naive_utc()),
        //             email_categories::dsl::unread_count.eq(email_categories::dsl::unread_count + 1), // Increment unread count
        //         ))
        //         .execute(conn);

        //         println!("unread count and latest email.");

        //         diesel::update(
        //             email_categories::dsl::email_categories
        //                 .filter(
        //                     email_categories::dsl::category_id
        //                         .eq(&new_email_category.sender_domain),
        //                 )
        //                 .filter(email_categories::dsl::child_full_domains.ne("")), // Ensure it's a primary category
        //         )
        //         .set((
        //             email_categories::dsl::latest_email_at.eq(chrono::Utc::now().naive_utc()),
        //             email_categories::dsl::unread_count.eq(email_categories::dsl::unread_count + 1), // Increment unread count
        //         ))
        //         .execute(conn);
        //         println!("updated number of of parent email category.");

        //         // }
        //     } else {
        //         println!("No category found, proceeding to check sender domain...");

        //         // Step 2: Check if the sender's domain matches an existing primary category
        //         let existing_primary_category = email_categories::dsl::email_categories
        //             .filter(
        //                 email_categories::dsl::category_id.eq(&new_email_category.sender_domain),
        //             )
        //             .filter(email_categories::dsl::child_full_domains.ne("")) // Ensure it's a primary category
        //             .filter(email_categories::dsl::parent_category_id.eq(""))

        //             .first::<EmailCategory>(conn)
        //             .optional()?;

        //         if let Some(primary_category) = existing_primary_category {
        //             println!("Existing primary category found:");
        //             // Insert the new category as a child of the primary category
        //             let mut child_category_associated_with_parent = new_email_category.clone();
        //             child_category_associated_with_parent.parent_category_id =
        //                 primary_category.category_id.clone();
        //                 child_category_associated_with_parent.child_full_domains = "".to_string();
        //             let new_email_category_id = new_email_category.category_id.clone();

        //             let mut parent_category_child_full_domains =
        //                 primary_category.child_full_domains.clone();
        //             // Get the existing domains, split by comma, and collect into a HashSet for uniqueness
        //             let mut existing_domains: Vec<String> = parent_category_child_full_domains
        //                 .as_deref()
        //                 .unwrap_or("")
        //                 .split(',')
        //                 .map(|s| s.to_string())
        //                 .collect();

        //             if !existing_domains.contains(&new_email_category_id) {
        //                 existing_domains.push(new_email_category_id.clone());
        //             }

        //             let updated_child_domains = existing_domains.join(",");

        //             // Now `parent_category_child_full_domains` contains the updated list

        //             diesel::insert_into(email_categories::table)
        //                 .values(child_category_associated_with_parent)
        //                 .execute(conn)?;

        //             println!("New child category inserted");

        //             // Update the unread count and latest_email_at of the parent category
        //             diesel::update(email_categories::dsl::email_categories.filter(
        //                 email_categories::dsl::category_id.eq(&primary_category.category_id),
        //             ))
        //             .filter(email_categories::dsl::child_full_domains.ne("")) // Ensure it's a primary category
        //             .filter(email_categories::dsl::parent_category_id.eq(""))

        //             .set((
        //                 email_categories::dsl::latest_email_at.eq(chrono::Utc::now().naive_utc()),
        //                 email_categories::dsl::unread_count
        //                     .eq(email_categories::dsl::unread_count + 1),
        //                 email_categories::dsl::child_full_domains.eq(updated_child_domains), // Set the updated child_full_domains
        //             ))
        //             .execute(conn)?;
        //         } else {
        //             // Insert the new category as a new primary category

        //             let mut new_parent_category = new_email_category.clone();

        //             new_parent_category.category_id = new_email_category.sender_domain.clone();

        //             let new_catagory_id = new_email_category.category_id.clone();

        //             let mut parent_category_child_full_domains =
        //                 new_email_category.category_id.clone();
        //                 new_parent_category.child_full_domains = if parent_category_child_full_domains.is_empty() {
        //                     "".to_string() // Assign an empty string
        //                 } else {
        //                     parent_category_child_full_domains.clone() // Assign the actual value
        //                 };
        //                 new_parent_category.parent_category_id = "".to_string(); // Or "".to_string()
        //             diesel::insert_into(email_categories::table)
        //                 .values(new_parent_category)
        //                 .execute(conn)?;

        //             let mut new_child_category = new_email_category.clone();

        //             new_child_category.child_full_domains = "".to_string();
        //             new_child_category.id = Uuid::new_v4().to_string();
        //             new_child_category.parent_category_id =
        //                 new_email_category.sender_domain.clone();

        //             diesel::insert_into(email_categories::table)
        //                 .values(new_child_category)
        //                 .execute(conn)?;

        //             println!(
        //                 "New primary category inserted: {:?}",
        //                 new_email_category.category_id
        //             );
        //         }
        //     }

        //     Ok(())
        // });

        match result {
            Ok(_) => {
                println!("Transaction committed successfully.");
                return Ok(());
            }
            Err(diesel::result::Error::DatabaseError(
                diesel::result::DatabaseErrorKind::Unknown,
                info,
            )) if info.message().contains("database is locked") => {
                // If the database is locked, retry after a delay
                attempts += 1;
                println!(
                    "Database is locked, attempt {}/{}. inside store_email_category Retrying in {}ms...",
                    attempts, max_attempts, backoff
                );
                thread::sleep(Duration::from_millis(backoff)); // Wait before retrying
                backoff *= 2; // Exponential backoff
            }
            Err(e) => {
                eprintln!("Error in transaction: {:?}", e);
                return Err(e); // For any other error, return immediately
            }
        }
    }

    Err(diesel::result::Error::DatabaseError(
        diesel::result::DatabaseErrorKind::Unknown,
        Box::new(
            "Max retry attempts reached, database is locked inside store_email_category"
                .to_string(),
        ),
    ))
}

pub fn delete_email_category(session_id: String) -> Result<(), diesel::result::Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, diesel::result::Error, _>(|conn| {
        diesel::delete(dsl::email_categories)
            .filter(crate::schema::email_categories::dsl::id.eq(session_id))
            .execute(conn)?;

        Ok(())
    })
}

pub fn list_email_categories() -> Result<Vec<EmailCategory>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // connection.transaction::<_, Error, _>(|conn| {
    //     // Query to fetch categories with parent_id as NULL (parent categories only)
    //     let categories = dsl::email_categories
    //         .filter(dsl::child_full_domains.ne("")) // Check where child_full_domains is an empty string
    //         .filter(email_categories::dsl::parent_category_id.eq(""))

    //         .order_by(dsl::latest_email_at.desc())
    //         .load::<EmailCategory>(conn)?;
    //     Ok(categories)
    // })
    let conn = connection;
    // let categories = dsl::email_categories
    //     .filter(dsl::child_full_domains.ne("")) // Primary categories only
    //     .filter(email_categories::dsl::parent_category_id.eq(""))
    //     .order_by(dsl::latest_email_at.desc())
    //     .load::<EmailCategory>(conn)?;
    let categories = dsl::email_categories
        .inner_join(email_dsl::emails.on(email_dsl::category.eq(dsl::category_id.nullable())))
        .filter(dsl::parent_category_id.eq(""))
        .filter(dsl::child_full_domains.ne("")) // Optional, if you want only primary categories
        .select(dsl::email_categories::all_columns()) // select only categories, not joined email columns
        .distinct() // ensure each category appears only once
        .order_by(dsl::latest_email_at.desc())
        .load::<EmailCategory>(conn)?;
    Ok(categories)
}

pub fn delete_all_email_categories() -> Result<usize, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Perform deletion of all rows in the email_categories table
        let deleted_rows = diesel::delete(dsl::email_categories).execute(conn)?;

        Ok(deleted_rows)
    })
}

pub fn count_all_email_categories() -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        let count = dsl::email_categories
            .filter(
                dsl::child_full_domains
                    .is_not_null()
                    .and(dsl::child_full_domains.ne("")),
            )
            .count()
            .get_result::<i64>(conn)?;
        Ok(count)
    })
}

pub fn list_child_categories(parent_id: String) -> Result<Vec<EmailCategory>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        let child_categories = dsl::email_categories
            .filter(dsl::parent_category_id.eq(parent_id))
            .order_by(dsl::name.asc())
            .load::<EmailCategory>(conn)?;

        Ok(child_categories)
    })
}
pub fn count_child_categories(parent_id: String) -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        let count = dsl::email_categories
            .filter(dsl::parent_category_id.eq(parent_id))
            .count()
            .get_result::<i64>(conn)?;

        Ok(count)
    })
}

// pub fn count_unread_emails_by_category() -> Result<Vec<(String, i64)>, Error> {
//     let connection = &mut establish_db_connection();

//     connection.transaction::<_, Error, _>(|conn| {
//         let unread_counts = dsl::email_categories
//             .select((dsl::category_id, dsl::unread_count))
//             .load::<(String, i64)>(conn)?;

//         Ok(unread_counts)
//     })
// }
