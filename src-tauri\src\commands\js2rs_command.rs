use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::check_and_refresh_token_unread::check_and_refresh_token_unread;
use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::app_data::AppData;
use crate::models::phone_context;
use crate::models::phone_context::PhoneCallContext;
use crate::models::user_data;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use crate::models::user_perference::EmailContext;
use crate::services::email_category_service::count_all_email_categories;
use crate::services::email_category_service::delete_all_email_categories;
use chrono::{ DateTime, Utc };
use serde_json::json;
use serde_json::Value;
use std::sync::Arc;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::sync::Notify;
use tokio::sync::RwLock;
use tracing::{ error, info, warn };

use crate::services::emails_service::count_unread_emails;
use crate::services::emails_service::delete_all_emails;

use crate::services::emails_service::count_total_emails;
use tokio_util::sync::CancellationToken;

use crate::google_api_functions::fetch_emails::save_progress_all_emails;
use crate::services::meetings_service::delete_all_meetings;
/// The Tauri command that gets called when Tauri `invoke` JavaScript API is called
#[tauri::command(async)]
pub async fn js2rs(
    handle: tauri::AppHandle,
    message: String,
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
    cancel_token: tauri::State<'_, Arc<CancellationToken>>
) -> Result<String, String> {
    info!(message, "message_handler: ");

    if message == "get_user" {
        //get the data from the mutex
        info!(message, "message_handler: message inside get_user ");
        // let app_data_arc = app_data.lock().await; // Lock before accessing user_data

        // // let app_data_guard = app_data.lock().unwrap();  // Lock the Mutex

        // let mut user_data = app_data_arc.user_data.lock().await;

        // let mut logged_in = app_data_arc.logged_in.lock().await;

        // if logged_in.clone() != true {
        //     match window.get_window("main") {
        //         Some(main_window) => {
        //             // this will high main application windo when siging in
        //             // main_window.hide();

        //             *logged_in = user_data.log_in(&window).await;

        //             // main_window.show();
        //         }
        //         _ => {}
        //     };
        // }
        // let user_name = user_data.user.name.clone();
        // let user_data_json_data = json!({
        //     "id": user_data.user.id,
        //     "email": user_data.user.email,
        //     "verified_email": user_data.user.verified_email,
        //     "name": user_name,
        //     "given_name": user_data.user.given_name,
        //     "family_name": user_data.user.family_name,
        //     "picture": user_data.user.picture,
        //     "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
        //     "expire_in": user_data.expire_in.map(|d| d.as_secs()),
        //     "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone()),
        // });
        // println!("this inside js2rs \n {:#?}", user_data_json_data);
        // Step 1: Acquire read locks for `user_data` and `logged_in`
        let (logged_in_status, user_data_snapshot, user_name) = {
            let app_data_arc = app_data.read().await; // Acquire read lock on `app_data`

            let mut logged_in = app_data_arc.logged_in.read().await;
            let user_data = app_data_arc.user_data.read().await; // Read `user_data`

            let user_name = user_data.user.name.clone();

            // Clone necessary data to minimize lock hold time
            let user_data_snapshot = user_data.clone();

            (*logged_in, user_data_snapshot, user_name)
        }; // Read locks are released here

        // Step 2: Check and update logged-in status if necessary
        let user_data = {
            let mut app_data_arc = app_data.write().await; // Acquire write lock on `app_data`
            let mut user_data = app_data_arc.user_data.write().await;
            let mut logged_in = app_data_arc.logged_in.write().await;

            if !logged_in_status {
                match handle.get_webview_window("auth") {
                    Some(main_window) => {
                        *logged_in = user_data.log_in(&handle).await;
                        // Log in the user
                    }
                    _ => {} // main_window.show();
                };
            }

            println!("this inside js2rs \n {:#?}", user_data.clone());
            user_data.clone()
        };
        println!("this inside js2rs \n {:#?}", user_data.clone());

        // let user_data_snapshot = user_data_snapshot.clone();

        // Step 3: Create the JSON response using the cloned data
        let user_data_json_data =
            json!({
            "id": user_data.user.id,
            "email": user_data.user.email,
            "verified_email": user_data.user.verified_email,
            "name": user_name,
            "given_name": user_data.user.given_name,
            "family_name": user_data.user.family_name,
            "picture": user_data.user.picture,
            "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
            "expire_in": user_data.expire_in.map(|d| d.as_secs()),
            "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone()),
        });
        println!("this inside js2rs \n {:#?}", user_data_json_data.clone());

        return Ok(user_data_json_data.to_string());
    }
    // if message == "add_new_email" {
    //     //get the data from the mutex
    //     info!(message, "message_handler: message inside get_user ");

    //     // Step 1: Acquire read locks for `user_data` and `logged_in`
    //     let (logged_in_status, user_data_snapshot, user_name) = {
    //         let app_data_arc = app_data.read().unwrap(); // Acquire read lock on `app_data`

    //         let logged_in = *app_data_arc.logged_in.lock().await;; // Read `logged_in` value
    //         let user_data = app_data_arc.user_data.read().unwrap(); // Read `user_data`

    //         let user_name = user_data.user.name.clone();

    //         // Clone necessary data to minimize lock hold time
    //         let user_data_snapshot = user_data.clone();

    //         (logged_in, user_data_snapshot, user_name)
    //     }; // Read locks are released here

    //     // Step 2: Check and update logged-in status if necessary
    //     if !logged_in_status {
    //         if let Some(main_window) = window.get_window("main") {
    //             // Perform the log-in operation
    //             // Hide and show the main window around the log-in operation if needed
    //             // main_window.hide();
    //             let mut app_data_arc = app_data.write().unwrap(); // Acquire write lock on `app_data`
    //             let mut logged_in = app_data_arc.logged_in;
    //             let mut user_data = app_data_arc.user_data.write().unwrap();

    //             *logged_in = user_data.log_in(&window).await; // Log in the user
    //                                                           // main_window.show();
    //         }
    //     }

    //     // Step 3: Create the JSON response using the cloned data
    //     let user_data_json_data = json!({
    //         "id": user_data_snapshot.user.id,
    //         "email": user_data_snapshot.user.email,
    //         "verified_email": user_data_snapshot.user.verified_email,
    //         "name": user_name,
    //         "given_name": user_data_snapshot.user.given_name,
    //         "family_name": user_data_snapshot.user.family_name,
    //         "picture": user_data_snapshot.user.picture,
    //         "refresh_token": user_data_snapshot.refresh_token.as_ref().map(|t| t.secret().clone()),
    //         "expire_in": user_data_snapshot.expire_in.map(|d| d.as_secs()),
    //         "access_token": user_data_snapshot.access_token.as_ref().map(|t| t.secret().clone()),
    //     });

    //     return Ok(user_data_json_data.to_string());
    //     // let app_data_arc = app_data.lock().await; // Lock before accessing user_data

    //     // // let app_data_guard = app_data.lock().unwrap();  // Lock the Mutex

    //     // let mut user_data = app_data_arc.user_data.lock().await;

    //     // let mut logged_in = app_data_arc.logged_in.lock().await;

    //     // if logged_in.clone() != true {
    //     //     match window.get_window("main") {
    //     //         Some(main_window) => {
    //     //             // this will high main application windo when siging in
    //     //             // main_window.hide();

    //     //             *logged_in = user_data.log_in_new_email(&window).await;

    //     //             // main_window.show();
    //     //         }
    //     //         _ => {}
    //     //     };
    //     // }

    //     // let user_name = user_data.user.name.clone();
    //     // let user_data_json_data = json!({
    //     //     "id": user_data.user.id,
    //     //     "email": user_data.user.email,
    //     //     "verified_email": user_data.user.verified_email,
    //     //     "name": user_name,
    //     //     "given_name": user_data.user.given_name,
    //     //     "family_name": user_data.user.family_name,
    //     //     "picture": user_data.user.picture,
    //     //     "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
    //     //     "expire_in": user_data.expire_in.map(|d| d.as_secs()),
    //     //     "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone()),
    //     // });
    //     // println!("this inside js2rs \n {:#?}", user_data_json_data);

    //     // return Ok(user_data_json_data.to_string());
    // }
    if message == "get_stored_user" {
        info!(message, "message_handler: message inside get_user check for retrieve function");

        // Step 1: Acquire a read lock for `app_data`
        let (email_context_json, phone_context_json, user_name, user_data_snapshot) = {
            let app_data_arc = app_data.read().await; // Acquire read lock on `app_data`

            let user_data = app_data_arc.user_data.read().await; // Read user_data

            // Clone necessary data to minimize lock hold time
            let email_context_json = user_data.email_context
                .clone()
                .map_or(String::new(), |context| context.to_json().to_string());

            let phone_context_json = user_data.phone_context
                .clone()
                .map_or(String::new(), |context| context.to_json().to_string());

            let user_name = user_data.user.name.clone();

            // Snapshot of user data for later JSON creation
            let user_data_snapshot = user_data.clone();

            (email_context_json, phone_context_json, user_name, user_data_snapshot)
        }; // Locks are released here

        // Step 2: Call external functions outside of any locks
        let unread_email_count = match count_unread_emails() {
            Ok(count) => count,
            Err(e) => {
                eprintln!("Failed to count unread emails: {}", e);
                return Err(e.to_string());
            }
        };

        let total_email_count = match count_total_emails() {
            Ok(count) => count,
            Err(e) => {
                eprintln!("Failed to count total emails: {}", e);
                return Err(e.to_string());
            }
        };

        let email_categories = match count_all_email_categories() {
            Ok(count) => count,
            Err(e) => {
                eprintln!("Failed to list email categories: {}", e);
                return Err(e.to_string());
            }
        };

        let email_context_parsed: Value = serde_json
            ::from_str(&email_context_json)
            .unwrap_or_else(|_| {
                eprintln!("❌ Failed to parse email_context_json");
                Value::Null
            });

        let phone_context_parsed: Value = serde_json
            ::from_str(&phone_context_json)
            .unwrap_or_else(|_| {
                eprintln!("❌ Failed to parse phone_context_json");
                Value::Null
            });

        // Step 3: Create the final JSON response
        let user_data_json_data =
            json!({
            "id": user_data_snapshot.user.id,
            "email": user_data_snapshot.user.email,
            "verified_email": user_data_snapshot.user.verified_email,
            "name": user_name,
            "given_name": user_data_snapshot.user.given_name,
            "family_name": user_data_snapshot.user.family_name,
            "picture": user_data_snapshot.user.picture,
            "refresh_token": user_data_snapshot.refresh_token.as_ref().map(|t| t.secret().clone()),
            "expire_in": user_data_snapshot.expire_in.map(|d| d.as_secs()),
            "access_token": user_data_snapshot.access_token.as_ref().map(|t| t.secret().clone()),
            "unread_email_count": unread_email_count,
            "total_email_count": total_email_count,
            "email_categories": email_categories,
            "email_context": email_context_parsed,
            "phone_context": phone_context_parsed,
        });

        println!("this inside js2rs \n {:#?}", user_data_json_data.clone());

        return Ok(user_data_json_data.to_string());

        // //get the data from the mutex
        // info!(
        //     message,
        //     "message_handler: message inside get_user check for retrive function"
        // );
        // let app_data_arc = app_data.lock().await; // Lock before accessing user_data

        // let mut logged_in = app_data_arc.logged_in.lock().await; // Lock the mutex
        // if !*logged_in {
        //     return Ok("".to_string());
        // }
        // // Lock app_data only to access user_data, email_context, and phone_context references
        // let user_data_arc;

        // {
        //     user_data_arc = Arc::clone(&app_data_arc.user_data);
        // } // Release app_data lock here

        // // Lock each context individually
        // let user_data = user_data_arc.lock().await;

        // let email_context_json = user_data.email_context.clone().unwrap().to_json();
        // let phone_context_json = user_data.phone_context.clone().unwrap().to_json();

        // let user_name = user_data.user.name.clone();

        // // Call count_unread_emails and save the result in a variable
        // let unread_email_count = match count_unread_emails() {
        //     Ok(count) => count,
        //     Err(e) => {
        //         eprintln!("Failed to count unread emails: {}", e);
        //         return Err(e.to_string());
        //     }
        // };

        // // Call count_total_emails and save the result in a variable
        // let total_email_count = match count_total_emails() {
        //     Ok(count) => count,
        //     Err(e) => {
        //         eprintln!("Failed to count total emails: {}", e);
        //         return Err(e.to_string());
        //     }
        // };

        // // Call count_all_email_categories and save the result in a variable
        // let email_categories = match count_all_email_categories() {
        //     Ok(count) => count,
        //     Err(e) => {
        //         eprintln!("Failed to list email categories: {}", e);
        //         return Err(e.to_string());
        //     }
        // };

        // let user_data_json_data = json!({
        //     "id": user_data.user.id,
        //     "email": user_data.user.email,
        //     "verified_email": user_data.user.verified_email,
        //     "name": user_name,
        //     "given_name": user_data.user.given_name,
        //     "family_name": user_data.user.family_name,
        //     "picture": user_data.user.picture,
        //     "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
        //     "expire_in": user_data.expire_in.map(|d| d.as_secs()),
        //     "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone()),
        //     "unread_email_count": unread_email_count,
        //     "total_email_count": total_email_count,
        //     "email_categories": email_categories,
        //     "email_context" : email_context_json,
        //     "phone_context": phone_context_json,
        // });
        // println!("this inside js2rs \n {:#?}", user_data_json_data.clone());

        // return Ok(user_data_json_data.to_string());
    }
    // if message == "get_email_context" {
    //     //get the data from the mutex
    //     info!(message, "message_handler: message inside get_user ");

    //     // let app_data_arc = app_data.lock().await; // Lock before accessing user_data

    //     // let email_context_json = app_data_arc.email_context.lock().await.to_json();
    //     let app_data_arc = app_data.lock().await; // Lock before accessing user_data
    //     let mut user_data: MutexGuard<'_, UserData> = app_data_arc.user_data.lock().await;

    //     let email_context_json = user_data.email_context.clone().unwrap().to_json();

    //     return Ok(email_context_json.to_string());
    // }
    // if message == "get_phone_context" {
    //     //get the data from the mutex
    //     info!(message, "message_handler: message inside get_user ");

    //     let app_data_arc = app_data.lock().await; // Lock before accessing user_data
    //     let mut user_data: MutexGuard<'_, UserData> = app_data_arc.user_data.lock().await;

    //     let phone_context_json = user_data.phone_context.clone().unwrap().to_json();

    //     return Ok(phone_context_json.to_string());
    // }
    if message == "get_email_context" {
        info!(message, "message_handler: message inside get_user - get_email_context");

        // Step 1: Acquire read lock for `app_data`
        let email_context_json = {
            let app_data_arc = app_data.read().await; // Acquire read lock for `app_data`
            let user_data = app_data_arc.user_data.read().await; // Acquire read lock for `user_data`

            // Clone email context and convert to JSON
            user_data.email_context.clone().unwrap().to_json()
        }; // Locks are released here

        return Ok(email_context_json.to_string());
    }

    if message == "get_phone_context" {
        info!(message, "message_handler: message inside get_user - get_phone_context");

        // Step 1: Acquire read lock for `app_data`
        let phone_context_json = {
            let app_data_arc = app_data.read().await; // Acquire read lock for `app_data`
            let user_data = app_data_arc.user_data.read().await; // Acquire read lock for `user_data`

            // Clone phone context and convert to JSON
            user_data.phone_context.clone().unwrap().to_json()
        }; // Locks are released here

        return Ok(phone_context_json.to_string());
    }
    if message == "get_fresh_accesstoken" {
        check_and_refresh_token(&app_data).await.map_err(|e|
            format!("Failed to refresh token: {}", e)
        )?;

        // // Lock app_data to access UserData
        // let user_data_arc = app_data.lock().await.user_data.clone(); // Lock before accessing user_data

        // // Step 2: Call `check_and_refresh_token_unread` with `user_data_arc`
        // check_and_refresh_token_unread(user_data_arc.clone())
        //     .await
        //     .map_err(|e| format!("Failed to refresh token: {}", e))?;

        // // Call check_and_refresh_token to ensure the access token is up to date
        // if let Err(e) = check_and_refresh_token(app_data.clone()).await {
        //     return Err(format!("Failed to refresh token: {}", e));
        // }
        // //get the data from the mutex
        info!(message, "message_handler: message inside get_fresh_accesstoen ");
        // Step 1: Acquire read lock to retrieve necessary data
        let user_data_json_data = {
            let app_data_arc = app_data.read().await; // Acquire read lock for `app_data`
            let user_data = app_data_arc.user_data.read().await; // Acquire read lock for `user_data`

            // Extract user details
            let user_name = user_data.user.name.clone();

            // Create JSON response using the read data
            let user_data_json =
                json!({
                "id": user_data.user.id,
                "email": user_data.user.email,
                "verified_email": user_data.user.verified_email,
                "name": user_name,
                "given_name": user_data.user.given_name,
                "family_name": user_data.user.family_name,
                "picture": user_data.user.picture,
                "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
                "expire_in": user_data.expire_in.map(|d| d.as_secs()),
                "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone())
            });

            user_data_json
        }; // Read locks are released here

        // Step 2: Use or log the retrieved data as needed
        println!("User data JSON: {}", user_data_json_data);

        // println!("this inside js2rs \n {:#?}", user_data_json_data);

        return Ok(user_data_json_data.to_string());
    }

    if message == "logout" {
        //get the data from the mutex
        // let mut user_data = app_data.user_data.lock().await;
        // let mut logged_in = app_data.logged_in.lock().await;
        let cancel_token: Arc<CancellationToken> = cancel_token.inner().clone();

        cancel_token.cancel(); // Properly cancel ongoing tasks
        println!("Canceled ongoing tasks.");

        info!(message, "message_handler: message inside get_user 1");

        //     let app_data_arc: MutexGuard<'_, AppData> = app_data.lock().await; // Lock before accessing user_data
        //     info!(message, "message_handler: message inside get_user -1");

        //     let mut user_data = app_data_arc.user_data.lock().await;
        //     info!(message, "message_handler: message inside get_user 0");

        //     info!(message, "message_handler: message inside get_user 1");

        //     let mut logged_in = app_data_arc.logged_in.lock().await;
        //     info!(message, "message_handler: message inside get_user 2");
        //     let mut user_logged_data = user_data.clone();

        //     //clear all user data
        //     user_data.user = User::new(
        //         "".to_string(),
        //         "".to_string(),
        //         true,
        //         "".to_string(),
        //         "".to_string(),
        //         "".to_string(),
        //         "".to_string(),
        //     );
        //     info!(message, "message_handler: message inside get_user 3");

        //     user_data.refresh_token = None;
        //     user_data.access_token = None;
        //     user_data.expire_in = None;
        //     user_data.email_context = None;
        //     user_data.phone_context = None;
        //     info!(message, "message_handler: message inside get_user 4");
        //     *logged_in = false;

        //     user_data.save_me();

        //     drop(user_logged_data);

        //     info!(message, "message_handler: message inside get_user 5");

        //     // *logged_in = user_data.log_in(&window).await;
        //     let user_name = user_data.user.name.clone();
        //     let user_data_json_data = json!({
        //         "id": user_data.user.id,
        //         "email": user_data.user.email,
        //         "verified_email": user_data.user.verified_email,
        //         "name": user_name,
        //         "given_name": user_data.user.given_name,
        //         "family_name": user_data.user.family_name,
        //         "picture": user_data.user.picture,
        //         "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
        //         "expire_in": user_data.expire_in.map(|d| d.as_secs()),
        //         "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone())
        //     });

        //     // Call to delete all user-related data
        //     if let Err(err) = delete_all_emails() {
        //         eprintln!("Failed to delete emails: {}", err);
        //     }

        //     if let Err(err) = delete_all_meetings() {
        //         eprintln!("Failed to delete meetings: {}", err);
        //     }

        //     if let Err(err) = delete_all_email_categories() {
        //         eprintln!("Failed to delete email categories: {}", err);
        //     }
        //     save_progress("null", None, false);
        //     return Ok(user_data_json_data.to_string());

        //     // return Ok(json!(user_data.user).to_string());
        // }

        // Step 1: Acquire write lock for `app_data`
        let mut app_data_arc = app_data.write().await;
        info!(message, "message_handler: message inside get_user -1");

        // Step 2: Acquire write lock for `user_data`
        let mut user_data = app_data_arc.user_data.write().await;
        info!(message, "message_handler: message inside get_user 0");
        let mut logged_in = app_data_arc.logged_in.write().await;
        *logged_in = false;

        // Step 3: Acquire write lock for `logged_in`
        //    app_data_arc.logged_in =   true.into();

        info!(message, "message_handler: message inside get_user 1");

        // Clone the current user data if needed for further use
        let mut user_logged_data = user_data.clone();

        // Step 4: Clear all user data
        user_data.user = User::new(
            "".to_string(),
            "".to_string(),
            true,
            "".to_string(),
            "".to_string(),
            Some("".to_string()),
            Some("".to_string())
        );
        info!(message, "message_handler: message inside get_user 3");

        // Clear tokens and contexts
        user_data.refresh_token = None;
        user_data.access_token = None;
        user_data.expire_in = None;
        user_data.email_context = None;
        user_data.phone_context = None;
        info!(message, "message_handler: message inside get_user 4");

        // Save user data changes
        user_data.save_me();

        drop(user_logged_data); // Explicitly drop cloned user data if no longer needed
        info!(message, "message_handler: message inside get_user 5");

        // Extract user information for JSON response
        let user_name = user_data.user.name.clone();
        let user_data_json_data =
            json!({
            "id": user_data.user.id,
            "email": user_data.user.email,
            "verified_email": user_data.user.verified_email,
            "name": user_name,
            "given_name": user_data.user.given_name,
            "family_name": user_data.user.family_name,
            "picture": user_data.user.picture,
            "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
            "expire_in": user_data.expire_in.map(|d| d.as_secs()),
            "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone())
        });

        // // Step 5: Delete all user-related data
        // if let Err(err) = delete_all_emails() {
        //     eprintln!("Failed to delete emails: {}", err);
        // }

        // if let Err(err) = delete_all_meetings() {
        //     eprintln!("Failed to delete meetings: {}", err);
        // }

        // if let Err(err) = delete_all_email_categories() {
        //     eprintln!("Failed to delete email categories: {}", err);
        // }

        // Step 6: Save progress
        // save_progress_all_emails("null", None, false);
    }
    // Return JSON response
    // Ok(user_data_json_data.to_string())

    //return else
    Ok("".to_string())
}

#[tauri::command(async)]
pub async fn authenticate_user(
    username: String,
    password: String,
    app_data: tauri::State<'_, Arc<RwLock<AppData>>> // Match this with fetch_unread_emails
) -> Result<String, String> {
    let api_url = "http://data.oway.life:8080/api/login/";

    let payload = json!({
        "username": username,
        "password": password
    });

    let client = reqwest::Client::new();
    let response = client.post(api_url).json(&payload).send().await;

    match response {
        Ok(res) if res.status().is_success() => {
            let response_json: Value = res
                .json().await
                .map_err(|e| {
                    format!("Deserialization error: {} - Ensure JSON response matches the expected structure.", e)
                })?;
            let mut is_paid = false;
            {
                // Acquire write lock for `app_data` to update its contents
                let mut app_data_arc = app_data.write().await;

                println!("Attempting to lock user_data...");
                let mut user_data = app_data_arc.user_data.write().await;
                println!("Successfully locked user_data");
                // Debug: Lock acquisition successful
                println!("Successfully acquired app_data and user_data locks");
                println!("Response JSON: {:#?}", response_json);
                // Extract user details
                let user = response_json.get("user").ok_or("Missing 'user' field in response")?;
                println!("User field in response: {:?}", user);

                let email_accounts = response_json
                    .get("email_accounts")
                    .ok_or("Missing 'email_accounts' field in response")?
                    .as_array()
                    .ok_or("Expected 'email_accounts' to be an array")?;
                println!("Email accounts: {:?}", email_accounts);

                let email = user.get("email").and_then(Value::as_str).unwrap_or("").to_string();
                println!("Extracted email: {}", email);

                let username = user
                    .get("username")
                    .and_then(Value::as_str)
                    .unwrap_or("")
                    .to_string();
                println!("Extracted username: {}", username);
                is_paid = user.get("is_paid").and_then(Value::as_bool).unwrap_or(false);
                // Update user details
                user_data.set(email.clone(), username.clone());
                println!("Updated user details in user_data");

                // Update refresh token
                if
                    let Some(refresh_token) = email_accounts
                        .get(0) // Assuming first account is the primary
                        .and_then(|account| account.get("refresh_token").and_then(Value::as_str))
                {
                    println!("Extracted refresh token: {}", refresh_token);
                    user_data.set_token(Some(oauth2::RefreshToken::new(refresh_token.to_string())));
                } else {
                    println!("No refresh token found in the first email account");
                }

                // Update access token
                if
                    let Some(access_token) = email_accounts
                        .get(0)
                        .and_then(|account| account.get("access_token").and_then(Value::as_str))
                {
                    println!("Extracted access token: {}", access_token);
                    user_data.set_access_token(
                        Some(oauth2::AccessToken::new(access_token.to_string()))
                    );
                } else {
                    println!("No access token found in the first email account");
                }

                // Update expiry
                if
                    let Some(expiry_str) = email_accounts
                        .get(0)
                        .and_then(|account| account.get("token_expiry").and_then(Value::as_str))
                {
                    println!("Extracted token expiry: {}", expiry_str);
                    if let Ok(expiry) = expiry_str.parse::<chrono::DateTime<Utc>>() {
                        let duration = expiry.signed_duration_since(Utc::now()).to_std().ok();
                        println!("Parsed expiry duration: {:?}", duration);
                        user_data.set_exprie_in(duration);
                    } else {
                        println!("Failed to parse token expiry: {}", expiry_str);
                    }
                } else {
                    println!("No token expiry found in the first email account");
                }
            }
            // Extract fields from the response
            let email_context = response_json.get("email_context").cloned().unwrap_or_default();
            let phone_context = response_json.get("phone_context").cloned().unwrap_or_default();
            println!("📨 Extracted email_context: {:#}", email_context);
            println!("📞 Extracted phone_context: {:#}", phone_context);
            {
                // Acquire write lock on app data
                let mut app_data_arc = app_data.write().await;
                let mut user_data = app_data_arc.user_data.write().await;

                // ✅ Set email context
                if let Some(ctx) = email_context.get(0) {
                    println!(
                        "🟢 Raw ctx (email_context[0]): {}",
                        serde_json::to_string_pretty(ctx).unwrap()
                    );

                    let mut ec = EmailContext::default_profile();
                    if let Err(e) = ec.update_from_json(ctx.clone()) {
                        println!("❌ Failed to update from JSON: {}", e);
                    } else {
                        user_data.set_email_context(Some(ec));
                        println!("✅ Email context successfully stored.");
                        println!("🧾 Final EmailContext: {:#?}", user_data.email_context);
                    }
                } else {
                    println!("⚠️ No email context found in response.");
                }

                // ✅ Set phone context
                if let Some(ctx) = phone_context.get(0) {
                    let mut pc = PhoneCallContext::default_context();
                    pc.update_from_json(ctx.clone());
                    user_data.set_phone_context(Some(pc));

                    println!("✅ Phone context successfully stored.");
                    println!("Stored Phone Context: {:?}", user_data.phone_context);
                } else {
                    println!("⚠️ No phone context found in response.");
                }

                user_data.set_issued_at();
            }

            println!("{}", response_json); // Log response JSON

            // Update issued_at timestamp
            // Create JSON response
            Ok(
                json!({
                "success": true,
                "user": "done",
                "is_paid": is_paid,
                "email_accounts": "done",
            }).to_string()
            )
            // let app_data_arc = app_data.lock().await;

            // println!("Attempting to lock user_data...");
            // let mut user_data = app_data_arc.user_data.lock().await;
            // println!("Successfully locked user_data");
            // // Debug: Lock acquisition successful
            // println!("Successfully acquired app_data and user_data locks");

            // // Extract user details
            // let user = response_json
            //     .get("user")
            //     .ok_or("Missing 'user' field in response")?;
            // println!("User field in response: {:?}", user);

            // let email_accounts = response_json
            //     .get("email_accounts")
            //     .ok_or("Missing 'email_accounts' field in response")?
            //     .as_array()
            //     .ok_or("Expected 'email_accounts' to be an array")?;
            // println!("Email accounts: {:?}", email_accounts);

            // let email = user
            //     .get("email")
            //     .and_then(Value::as_str)
            //     .unwrap_or("")
            //     .to_string();
            // println!("Extracted email: {}", email);

            // let username = user
            //     .get("username")
            //     .and_then(Value::as_str)
            //     .unwrap_or("")
            //     .to_string();
            // println!("Extracted username: {}", username);

            // // Update user details
            // user_data.set(email.clone(), username.clone());
            // println!("Updated user details in user_data");

            // // Update refresh token
            // if let Some(refresh_token) = email_accounts
            //     .get(0) // Assuming first account is the primary
            //     .and_then(|account| account.get("refresh_token").and_then(Value::as_str))
            // {
            //     println!("Extracted refresh token: {}", refresh_token);
            //     user_data.set_token(Some(oauth2::RefreshToken::new(refresh_token.to_string())));
            // } else {
            //     println!("No refresh token found in the first email account");
            // }

            // // Update access token
            // if let Some(access_token) = email_accounts
            //     .get(0)
            //     .and_then(|account| account.get("access_token").and_then(Value::as_str))
            // {
            //     println!("Extracted access token: {}", access_token);
            //     user_data
            //         .set_access_token(Some(oauth2::AccessToken::new(access_token.to_string())));
            // } else {
            //     println!("No access token found in the first email account");
            // }

            // // Update expiry
            // if let Some(expiry_str) = email_accounts
            //     .get(0)
            //     .and_then(|account| account.get("token_expiry").and_then(Value::as_str))
            // {
            //     println!("Extracted token expiry: {}", expiry_str);
            //     if let Ok(expiry) = expiry_str.parse::<chrono::DateTime<Utc>>() {
            //         let duration = expiry.signed_duration_since(Utc::now()).to_std().ok();
            //         println!("Parsed expiry duration: {:?}", duration);
            //         user_data.set_exprie_in(duration);
            //     } else {
            //         println!("Failed to parse token expiry: {}", expiry_str);
            //     }
            // } else {
            //     println!("No token expiry found in the first email account");
            // }
            // println!("{}", response_json); // Lock app_data

            // // Update issued_at timestamp
            // user_data.set_issued_at();

            // // Mark user as logged in
            // {
            //     let mut logged_in = app_data_arc.logged_in.lock().await; // Lock the mutex
            //     *logged_in = true; // Update the value inside the mutex
            // } // Create JSON response
            // return Ok(json!({
            //     "success": true,
            //     "user": "done",
            //     "email_accounts": "done",
            // })
            // .to_string());
        }
        Ok(res) => {
            // Handle specific error scenarios
            // Extract the status before consuming the response
            let status = res.status(); // Save the status for later use

            // Consume the response to extract the body text
            let error_message = res.text().await.unwrap_or_else(|_| "Unknown error".to_string());

            // Use the previously saved status
            let error_code = match status.as_u16() {
                401 => "INCORRECT_PASSWORD",
                404 => "USER_NOT_FOUND",
                403 => "ACCOUNT_LOCKED",
                _ => "SERVER_ERROR",
            };

            return Ok(
                json!({
                "success": false,
                "error_code": error_code,
                "error": error_message,
            }).to_string()
            );
        }
        Err(e) => Err(format!("Failed to connect to server: {}", e)),
    }
}

#[tauri::command]
pub async fn initialize_contexts(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
    email_context: Value, // JSON value for email context
    phone_context: Value // JSON value for phone context
) -> Result<String, String> {
    // API URL for storage
    let api_url = "http://data.oway.life:8080/api/store-user-contexts/";

    // Print the email context for debugging
    println!("Email Context Received: {:?}", email_context);

    // Print the phone context for debugging
    println!("Phone Context Received: {:?}", phone_context);

    // Lock app_data and update contexts
    // let mut app_data_arc = app_data.lock().await;

    // Lock user_data for further updates
    // let mut user_data = app_data_arc.user_data.lock().await;

    let mut email: String = String::new();
    let mut name: String = String::new();

    // Step 1: Acquire read lock to extract email and name
    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        email = user_data.user.email.clone();
        name = user_data.user.name.clone();
    } // Read lock released here

    // Step 2: Acquire write lock to update email and phone contexts
    {
        let mut app_data_arc = app_data.write().await; // Acquire write lock
        let mut user_data = app_data_arc.user_data.write().await;

        let mut email_context_var = EmailContext::default_profile();
        email_context_var.update_from_json(email_context.clone());
        user_data.set_email_context(Some(email_context_var));

        let mut ph_context_var = PhoneCallContext::default_context();
        ph_context_var.update_from_json(phone_context.clone());
        user_data.set_phone_context(Some(ph_context_var));
    } // Write lock released here

    // Retrieve and prepare user details
    // let user_name = user_data.user.name.clone();
    // let mut email_context_var = EmailContext::default_profile();
    // email_context_var.update_from_json(email_context.clone());
    // user_data.set_email_context(Some(email_context_var));

    // let mut ph_context_var = PhoneCallContext::default_context();
    // ph_context_var.update_from_json(phone_context.clone());
    // user_data.set_phone_context(Some(ph_context_var));

    let user =
        json!({
        "username": email.split('@').next().unwrap_or("unknown").to_string(),
        "email": email,
        "name": name,
        "phone_number": "+19383481077"
    });

    // Log user info (if needed for debugging)
    // println!("User details: {:?}", user_data);

    let payload =
        json!({
        "user": user,
        "email_context": email_context,
        "phone_context": phone_context
    });
    // Send the contexts to the API for storage
    let client = reqwest::Client::new();
    let response = client
        .post(api_url)
        .json(&payload)
        .send().await
        .map_err(|err| format!("Error sending request: {:?}", err))?;

    if !response.status().is_success() {
        return Err(
            format!(
                "API returned an error: {:?}",
                response.text().await.unwrap_or_else(|_| "Unknown error".to_string())
            )
        );
    }

    return Ok("".to_string());
}

// #[tauri::command]
// pub async fn update_email_contexts(
//     app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
//     email_context: Value,                             // JSON value for email context
// ) -> Result<String, String> {
//     // API URL for storage
//     let api_url = "http://localhost:9000/api/email-contexts/update-by-email/";

//     // Print the email context for debugging
//     println!("Email Context Received: {:?}", email_context);

//     // Lock app_data and update contexts
//     // let mut app_data_arc = app_data.lock().await;

//     // Lock user_data for further updates
//     // let mut user_data = app_data_arc.user_data.lock().await;

//     let mut email: String = String::new();
//     let mut name: String = String::new();

//     // Step 1: Acquire read lock to extract email and name
//     {
//         let app_data_arc = app_data.read().await; // Acquire read lock
//         let user_data = app_data_arc.user_data.read().await;

//         email = user_data.user.email.clone();
//         name = user_data.user.name.clone();
//     } // Read lock released here

//     // Step 2: Acquire write lock to update email and phone contexts
//     {
//         let mut app_data_arc = app_data.write().await; // Acquire write lock
//         let mut user_data = app_data_arc.user_data.write().await;

//         let mut email_context_var = EmailContext::default_profile();
//         email_context_var.update_from_json(email_context.clone());
//         user_data.set_email_context(Some(email_context_var));
//     } // Write lock released here

//     // Retrieve and prepare user details
//     // let user_name = user_data.user.name.clone();
//     // let mut email_context_var = EmailContext::default_profile();
//     // email_context_var.update_from_json(email_context.clone());
//     // user_data.set_email_context(Some(email_context_var));

//     // let mut ph_context_var = PhoneCallContext::default_context();
//     // ph_context_var.update_from_json(phone_context.clone());
//     // user_data.set_phone_context(Some(ph_context_var));

//     let user = json!({
//         "username": email.split('@').next().unwrap_or("unknown").to_string(),
//         "email": email,
//         "name": name,
//         "phone_number": "+19383481077"
//     });

//     // Log user info (if needed for debugging)
//     // println!("User details: {:?}", user_data);

//     let payload = json!({
//         "user": user,
//         "email_context": email_context,
//         "phone_context": phone_context
//     });
//     // Send the contexts to the API for storage
//     let client = reqwest::Client::new();
//     let response = client
//         .post(api_url)
//         .json(&payload)
//         .send()
//         .await
//         .map_err(|err| format!("Error sending request: {:?}", err))?;

//     if !response.status().is_success() {
//         return Err(format!(
//             "API returned an error: {:?}",
//             response
//                 .text()
//                 .await
//                 .unwrap_or_else(|_| "Unknown error".to_string())
//         ));
//     }

//     return Ok("".to_string());
// }

#[tauri::command]
pub async fn set_password(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
    password: Value // JSON value for phone context
) -> Result<String, String> {
    let mut email: String = String::new();

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        email = user_data.user.email.clone();
    }
    // API URL for storage
    let api_url = "http://data.oway.life:8080/api/users/update_password/";

    // Lock app_data and update contexts

    // Lock user_data for further updates

    // Log user info (if needed for debugging)
    // println!("User details: {:?}", user_data);

    let payload = json!({
        "email": email,
        "new_password": password,
    });
    // Send the contexts to the API for storage
    let client = reqwest::Client::new();
    let response = client
        .post(api_url)
        .json(&payload)
        .send().await
        .map_err(|err| format!("Error sending request: {:?}", err))?;

    if !response.status().is_success() {
        return Err(
            format!(
                "API returned an error: {:?}",
                response.text().await.unwrap_or_else(|_| "Unknown error".to_string())
            )
        );
    }

    return Ok("".to_string());
}
