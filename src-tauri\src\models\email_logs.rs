use crate::schema::email_logs;
use serde::{Deserialize, Serialize};
use diesel::{Insertable, Queryable, Identifiable};
use chrono::NaiveDateTime;

#[derive(Insertable, Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = email_logs)]
pub struct EmailLog {
    pub id: String,
    pub email_id: String,
    pub snoozed_until: NaiveDateTime,
    pub created_at: Option<NaiveDateTime>,
    pub resurfaced: Option<bool>,
}