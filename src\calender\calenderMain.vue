<template>
  <div class="relative z-0 flex flex-col w-full h-full max-h-full overflow-hidden bg-primary">
    <!-- Tab Navigation -->
    <div class="flex justify-between border-b-2 border-[#ddd] px-2">
      <div class="w-80">
        <button @click="setTab('upcoming')" :class="tabClass('upcoming')" class="pt-1 pb-3 px-4">Upcoming</button>
        <button @click="setTab('pending')" :class="tabClass('pending')" class="py-2 px-4">Pending</button>
        <button @click="setTab('past')" :class="tabClass('past')" class="py-2 px-4">Past</button>
      </div>
      <!-- <div class="grow relative flex justify-center items-center">
        <DayTimeZone />
      </div> -->

      <div class="flex justify-end items-center gap-2 w-80">
        <button
          @click="showAvailabities = true"
          class="flex bg-dark-400 hover:bg-dark-500 transition-all duration-200 py-2 px-2 rounded-md text-white gap-1 justify-center items-center"
        >
          <ClockIcon class="size-5" />
          <p class="text-sm">Availability</p>
        </button>
        <button
          @click="showEvents = true"
          class="flex bg-dark-400 hover:bg-dark-500 transition-all duration-200 py-2 px-2 rounded-md text-white gap-1 justify-center items-center"
        >
          <CalendarDaysIcon class="size-5" />
          <p class="text-sm">Events</p>
        </button>
        <button
          @click="showMeetingBot = true"
          class="flex bg-amber-700 hover:bg-amber-600 transition-all duration-200 py-2 px-2 rounded-md text-white gap-1 justify-center items-center"
        >
          <Icon icon="fluent:bot-48-filled" class="size-4 mb-0.5" />
          <p class="text-sm">Meeting Bot</p>
        </button>
      </div>
    </div>

    <!-- Content based on active tab -->
    <div class="w-full h-[calc(100%-50px)]">
      <div v-if="activeTab === 'upcoming'" class="size-full flex flex-col items-center justify-center">
        <UpcomingTab />
      </div>

      <div v-if="activeTab === 'pending'" class="size-full flex flex-col items-center justify-center">
        <PendingTab />
      </div>

      <div v-if="activeTab === 'past'" class="size-full flex flex-col items-center justify-center">
        <PastTab />
      </div>
    </div>

    <div v-show="showAvailabities" class="bg-primary size-full absolute z-10">
      <AvailabitiyModal v-on:close="showAvailabities = false" />
    </div>

    <div v-show="showEvents" class="bg-primary size-full absolute z-10">
      <EventModal v-on:close="showEvents = false" />
    </div>

    <div v-show="showMeetingBot" class="bg-white/5 size-full absolute z-10 flex justify-center items-center">
      <div class="w-1/2 h-auto bg-secondary-200 rounded-md">
        <MeetingBotModal v-on:close="showMeetingBot = false" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import PendingTab from "./PendingTab.vue";
import UpcomingTab from "./UpcomingTab.vue";
import PastTab from "./PastTab.vue";
import { CalendarDaysIcon, ClockIcon } from "@heroicons/vue/24/outline";
import EventModal from "./ui/EventModal.vue";
// import DayTimeZone from "../components/svgs/DayTimeZone/DayTimeZone.vue";
import AvailabitiyModal from "./modals/availablities/AvailabitiyModal.vue";
import { useBookingStore } from "../stores/bookingsStore";
import MeetingBotModal from "./modals/meeting-bot-modal/MeetingBotModal.vue";
import { Icon } from "@iconify/vue/dist/iconify.js";

const activeTab = ref("upcoming");
const showAvailabities = ref(false);
const showEvents = ref(false);
const showMeetingBot = ref(false);

const bookingStore = useBookingStore();

function setTab(tab: string) {
  activeTab.value = tab;
}

function tabClass(tab: string) {
  return activeTab.value === tab
    ? "border-b-[#BF997C] border-b-2 text-[#BF997C]"
    : "border-transparent text-[#BF997C]/50";
}

onMounted(async () => {
  await bookingStore.fetchBookings();
});
</script>

<style>
@import "qalendar/dist/style.css";
.calendar-container {
  max-width: 100%;
  margin: auto;
  padding: 20px;
  height: 100vh;
}
</style>
