<template>
  <div class="flex size-full items-center justify-center text-slate-500">
    <div class="text-xs font-semibold flex-1">{{ props.name }}</div>
    <div class="group flex-1 flex justify-end items-center gap-2">
      <Input :value="props.value" :type="props.type" :editing="props.editing" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from "@iconify/vue/dist/iconify.js";
import { ref } from "vue";
import Input from "../components/input/input.vue";

const props = defineProps({
  name: String,
  value: String,
  editing: {
    type: Boolean,
    default: false,
  },
  attr: String,
  type: {
    type: String,
    default: "text",
  },
});
</script>

<style scoped></style>
