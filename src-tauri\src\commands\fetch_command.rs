use crate::email_generator::generator_entry::email_generator_function_chatgpt;
use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::check_and_refresh_token_unread::check_and_refresh_token_unread;
use crate::google_api_functions::fetch_emails::process_emails_with_flag;
use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use crate::models::user_perference::EmailContext;
use chrono::{ DateTime, Utc };
use oauth2::basic::BasicTokenType;
use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    Author<PERSON><PERSON><PERSON>,
    ClientId,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>srf<PERSON><PERSON>,
    EmptyExtraT<PERSON><PERSON>ields,
    <PERSON>kceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    <PERSON>ope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};
use serde_json::json;
use std::error::Error;
use std::sync::Arc;
use std::time::{ Duration, Instant };
use tauri::State;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::sync::RwLock;
use tokio::task;
use tokio_util::sync::CancellationToken;
use tracing::{ error, info, warn };

use crate::email_generator::generator_entry::email_gnerator_function;
use crate::email_generator::generator_entry::email_subjet_gnerator_function;

use crate::google_api_functions::fetch_emails::fetch_emails;
// use rust_bert::pipelines::common::ModelType;

use crate::google_api_functions::fetch_emails::load_progress;
// use rust_bert::pipelines::text_generation::{TextGenerationConfig, TextGenerationModel};

#[tauri::command(async)]
pub async fn fetch_all_emails(
    message: String,
    tone: String,
    key_points: String,
    enable_email_context: Option<bool>,
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
    cancel_token: tauri::State<'_, Arc<CancellationToken>>
) -> Result<String, String> {
    info!(message, "message_handler: ");
    let timer: u64 = 10;
    if message == "generate_email" {
        let mut email_context: Option<EmailContext> = None;

        {
            let app_data_arc = app_data.read().await; // Acquire read lock
            let user_data = app_data_arc.user_data.read().await;

            email_context = user_data.email_context.clone(); // Clone if `Duration` is needed later
        } // Lock is released here automatically

        // let mut email_context = app_data_arc.email_context.lock().await;
        // let mut email_context = user_data.email_context.clone().await;

        // Update and save the profile with new tone and key points
        // email_context
        //     .await
        //     .update_tone_and_key_points(tone.clone(), key_points.clone());

        // let generated_text =
        //     match email_gnerator_function(&key_points, &tone, &email_context.unwrap()).await {
        //         Ok(text) => text,
        //         Err(e) => {
        //             eprintln!("Error generating email: {:?}", e);
        //             return Err(e.to_string()); // Or handle the error in a way that's suitable for your case
        //         }
        //     };

        let enable_context = enable_email_context.or_else(|| Some(true)).unwrap();

        let generated_text = email_generator_function_chatgpt(
            &key_points,
            &tone,
            &email_context.unwrap(),
            enable_context
        ).await.map_err(|e| {
            eprintln!("Error generating email: {:?}", e);
            e.to_string() // Convert the error into a string
        })?;

        // let generated_subject = match email_subjet_gnerator_function(&generated_text).await {
        //     Ok(text) => text,
        //     Err(e) => {
        //         eprintln!("Error generating email: {:?}", e);
        //         return Err(e.to_string()); // Or handle the error in a way that's suitable for your case
        //     }
        // };

        let generated_subject = email_subjet_gnerator_function(&generated_text).await.map_err(|e| {
            eprintln!("Error generating email subject: {:?}", e);
            e.to_string()
        })?;

        // Create JSON with the generated text
        let json_data =
            json!({
            "email": generated_text,//"generated_text".to_string(),
            "subject": "generated_subject",
        });

        // return Ok(json_data.to_string());
        return Ok(generated_text);
    }
    if message == "fetch_emails" {
        check_and_refresh_token(&app_data);

        let mut exprie_in_read: Option<Duration> = None;
        let mut issued_at_read: Option<DateTime<Utc>> = None;
        let mut refresh_token: Option<String> = None;
        let mut access_token: Option<String> = None;

        {
            let app_data_arc = app_data.read().await; // Acquire read lock
            let user_data = app_data_arc.user_data.read().await;

            exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
            issued_at_read = user_data.issued_at.clone();
            refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
            access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
        } // Lock is released here automatically

        // println!("this inside fetch_emails_periodically\n {:#?}", user_name);

        // let user_data_json_data = json!({
        //     "id": user_data.user.id,
        //     "email": user_data.user.email,
        //     "verified_email": user_data.user.verified_email,
        //     "name": user_name,
        //     "given_name": user_data.user.given_name,
        //     "family_name": user_data.user.family_name,
        //     "picture": user_data.user.picture,
        //     "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
        //     "expire_in": user_data.expire_in.map(|d| d.as_secs()),
        //     "issued_at": user_data.issued_at.map(|dt| dt.to_rfc3339()),
        //     "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone())
        // });
        // println!(
        //     "this inside fetch_emails_periodically \n {:#?}",
        //     user_data_json_data
        // );

        // Check if the access token is expired and refresh if needed
        if
            let (Some(issued_at), Some(expire_in), Some(refresh_token)) = (
                issued_at_read,
                exprie_in_read,
                refresh_token.clone(),
            )
        {
            // println!(
            //     "My access tokent did exprie lets create a new access token \n {:#?}",
            //     refresh_token.clone()
            // );
            let expiry_time = issued_at + expire_in;
            if chrono::Utc::now() >= expiry_time {
                println!("Access token expired, refreshing...");

                // Step 3: Refresh the token outside the lock scope
                match refresh_access_token(&refresh_token).await {
                    Ok(new_token) => {
                        // Step 4: Acquire write lock to update user data
                        let mut app_data_write = app_data.write().await;
                        let mut user_data = app_data_write.user_data.write().await;

                        user_data.access_token = Some(oauth2::AccessToken::new(new_token));
                        user_data.set_issued_at(); // Update issued_at time
                        user_data.save_me(); // Save changes
                        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());

                        println!("Access token refreshed and updated.");
                    }
                    Err(err) => {
                        println!("Failed to refresh access token: {:?}", err);
                        // return Err(err.to_string()); // Propagate the error upwards
                        // return Ok("finished getting all of your emails".to_string());
                    }
                }
            }
        }

        let progress_data = load_progress().unwrap_or_else(|_| json!({}));
        let saved_next_page_token = progress_data.get("nextPageToken").and_then(|v| v.as_str());
        let saved_all_emails_fetched = progress_data
            .get("allEmailsFetched")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);
        println!("Now lets start getting all emails .... ");

        if saved_all_emails_fetched {
            println!("All emails have already been fetched. Exiting fetch.");
            return Ok("All emails have already been fetched. Exiting fetch.".to_string());
        }
        let cancel_token: Arc<CancellationToken> = cancel_token.inner().clone();
        if cancel_token.is_cancelled() {
            println!("Fetch emails cancelled.");
            return Ok("All emails are fetched".to_string());
        }
        let app_data_clone = app_data.inner().clone(); // Extract and clone the Arc

        tokio::spawn(async move {
            if let (Some(refresh_token), Some(access_token)) = (refresh_token, access_token) {
                if
                    let Err(err) = fetch_emails(
                        app_data_clone,
                        &refresh_token,
                        &access_token,
                        "https://www.googleapis.com/gmail/v1/users/me/messages".to_string(),
                        cancel_token
                    ).await
                {
                    println!("Error in fetch_emails: {:?}", err);
                }
            } else {
                println!("Missing tokens; cannot fetch emails.");
            }
        });
        process_emails_with_flag(app_data).await;

        // fetch_emails(
        //     app_data.inner().clone(),
        //     &refresh_token.as_ref().unwrap(),
        //     &access_token.as_ref().unwrap(),
        //     "https://www.googleapis.com/gmail/v1/users/me/messages".to_string(),
        //     cancel_token
        // )
        // .await;

        return Ok("finished getting all of your emails".to_string());
    }
    if message == "fetch_send_emails" {
        check_and_refresh_token(&app_data);

        let mut exprie_in_read: Option<Duration> = None;
        let mut issued_at_read: Option<DateTime<Utc>> = None;
        let mut refresh_token: Option<String> = None;
        let mut access_token: Option<String> = None;

        {
            let app_data_arc = app_data.read().await; // Acquire read lock
            let user_data = app_data_arc.user_data.read().await;

            exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
            issued_at_read = user_data.issued_at.clone();
            refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
            access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
        } // Lock is released here automatically

        // Check if the access token is expired and refresh if needed
        if
            let (Some(issued_at), Some(expire_in), Some(refresh_token)) = (
                issued_at_read,
                exprie_in_read,
                refresh_token.clone(),
            )
        {
            println!(
                "My access tokent did exprie lets create a new access token \n {:#?}",
                refresh_token.clone()
            );
            let expiry_time = issued_at + expire_in;
            if chrono::Utc::now() >= expiry_time {
                println!("Access token expired, refreshing...");

                // Step 3: Refresh the token outside the lock scope
                match refresh_access_token(&refresh_token).await {
                    Ok(new_token) => {
                        // Step 4: Acquire write lock to update user data
                        let mut app_data_write = app_data.write().await;
                        let mut user_data = app_data_write.user_data.write().await;

                        user_data.access_token = Some(oauth2::AccessToken::new(new_token));
                        user_data.set_issued_at(); // Update issued_at time
                        user_data.save_me(); // Save changes
                        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());

                        println!("Access token refreshed and updated.");
                    }
                    Err(err) => {
                        println!("Failed to refresh access token: {:?}", err);
                        // return Err(err.to_string()); // Propagate the error upwards
                        // return Ok("finished getting all of your emails".to_string());
                    }
                }
            }
        }

        let cancel_token: Arc<CancellationToken> = cancel_token.inner().clone();
        if cancel_token.is_cancelled() {
            println!("Fetch emails cancelled.");
            return Ok("All emails are fetched".to_string());
        }
        let app_data_clone = app_data.inner().clone(); // Extract and clone the Arc

        tokio::spawn(async move {
            if let (Some(refresh_token), Some(access_token)) = (refresh_token, access_token) {
                if
                    let Err(err) = fetch_emails(
                        app_data_clone,
                        &refresh_token,
                        &access_token,
                        "https://www.googleapis.com/gmail/v1/users/me/messages?q=is:sent".to_string(),
                        cancel_token
                    ).await
                {
                    println!("Error in fetch_emails: {:?}", err);
                }
            } else {
                println!("Missing tokens; cannot fetch emails.");
            }
        });

        return Ok("finished getting all of your emails".to_string());
    }

    //return else
    return Ok("".to_string());
}

// async fn fetch_calendar_events(
//     app_data: Arc<Mutex<AppData>>,
//     calendar_api_url: &str,
// ) -> Result<String, Box<dyn std::error::Error>> {
//     // Lock the app data and get user data
//     let app_data_arc = app_data.lock().await;
//     let mut user_data = app_data_arc.user_data.lock().await;

//     // Serialize user data to JSON for debugging or response
//     let user_data_json_data = json!({
//         "id": user_data.user.id,
//         "email": user_data.user.email,
//         "verified_email": user_data.user.verified_email,
//         "name": user_data.user.name.clone(),
//         "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone()),
//         "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
//         "expire_in": user_data.expire_in.map(|d| d.as_secs()),
//         "issued_at": user_data.issued_at.map(|dt| dt.to_rfc3339()),
//     });

//     println!("User data: {:#?}", user_data_json_data);

//     // Refresh the access token if expired
//     if let (Some(issued_at), Some(expire_in)) = (user_data.issued_at, user_data.expire_in) {
//         let expiry_time = issued_at + expire_in;
//         if chrono::Utc::now() >= expiry_time {
//             println!("Access token expired. Refreshing...");

//             if let Some(refresh_token) = &user_data.refresh_token {
//                 if let Ok(new_token) = refresh_access_token(refresh_token.secret()).await {
//                     println!("New token: {:#?}", new_token);

//                     user_data.access_token = Some(new_token);
//                     user_data.set_issued_at();
//                     user_data.save_me();
//                 } else {
//                     return Err("Failed to refresh access token.".into());
//                 }
//             }
//         }
//     }

//     // Use the access token to fetch events
//     if let Some(access_token) = &user_data.access_token {
//         let client = reqwest::Client::new();
//         let response = client
//             .get(calendar_api_url)
//             .header("Authorization", format!("Bearer {}", access_token.secret()))
//             .query(&[
//                 ("calendarId", "primary"),
//                 ("timeMin", chrono::Utc::now().to_rfc3339().as_str()),
//                 ("singleEvents", "true"),
//                 ("orderBy", "startTime"),
//             ])
//             .send()
//             .await?;

//         if response.status().is_success() {
//             let events = response.text().await?;
//             println!("Fetched events: {:#?}", events);

//             // Optionally save or process events here
//             return Ok(events);
//         } else {
//             eprintln!(
//                 "Failed to fetch events. Status: {}, Body: {}",
//                 response.status(),
//                 response.text().await?
//             );
//             return Err("Failed to fetch events.".into());
//         }
//     } else {
//         return Err("Access token is missing.".into());
//     }
// }

// async fn create_calendar_event(
//     access_token: &str,
//     event_data: &serde_json::Value,
// ) -> Result<String, Box<dyn std::error::Error>> {
//     let client = reqwest::Client::new();
//     let response = client
//         .post("https://www.googleapis.com/calendar/v3/calendars/primary/events")
//         .header("Authorization", format!("Bearer {}", access_token))
//         .json(event_data)
//         .send()
//         .await?;

//     if response.status().is_success() {
//         let created_event = response.text().await?;
//         Ok(created_event)
//     } else {
//         Err(format!(
//             "Failed to create event. Status: {}, Body: {}",
//             response.status(),
//             response.text().await?
//         )
//         .into())
//     }
// }

async fn delete_calendar_event(
    access_token: &str,
    event_id: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let response = client
        .delete(
            format!("https://www.googleapis.com/calendar/v3/calendars/primary/events/{}", event_id)
        )
        .header("Authorization", format!("Bearer {}", access_token))
        .send().await?;

    if response.status().is_success() {
        Ok(())
    } else {
        Err(
            format!(
                "Failed to delete event. Status: {}, Body: {}",
                response.status(),
                response.text().await?
            ).into()
        )
    }
}

async fn fetch_event_details(
    access_token: &str,
    event_id: &str
) -> Result<serde_json::Value, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let response = client
        .get(
            &format!("https://www.googleapis.com/calendar/v3/calendars/primary/events/{}", event_id)
        )
        .header("Authorization", format!("Bearer {}", access_token))
        .send().await?;

    if response.status().is_success() {
        let event_details: serde_json::Value = response.json().await?;
        Ok(event_details)
    } else {
        Err(
            format!(
                "Failed to fetch event details. Status: {}, Body: {}",
                response.status(),
                response.text().await?
            ).into()
        )
    }
}

async fn fetch_recurring_events(
    access_token: &str,
    calendar_id: &str
) -> Result<Vec<serde_json::Value>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let response = client
        .get(&format!("https://www.googleapis.com/calendar/v3/calendars/{}/events", calendar_id))
        .header("Authorization", format!("Bearer {}", access_token))
        .query(
            &[
                ("singleEvents", "true"),
                ("timeMin", chrono::Utc::now().to_rfc3339().as_str()),
                ("orderBy", "startTime"),
            ]
        )
        .send().await?;

    if response.status().is_success() {
        let data: serde_json::Value = response.json().await?;
        let events = data["items"]
            .as_array()
            .unwrap_or(&vec![])
            .clone();
        Ok(events)
    } else {
        Err(
            format!(
                "Failed to fetch recurring events. Status: {}, Body: {}",
                response.status(),
                response.text().await?
            ).into()
        )
    }
}

#[tauri::command]
pub async fn test_process_emails(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>
) -> Result<(), String> {
    println!("###    testing process Eamils    ###");
    process_emails_with_flag(app_data).await;
    Ok(())
}
