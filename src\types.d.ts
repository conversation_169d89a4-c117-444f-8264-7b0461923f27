import { Serializer, Deserializer } from "./serializer-interfaces"; // You might need to create this

export type Session = {
  id: string;
  name: string;
  created_at: string;
};

export type Message = {
  id: string;
  session_id: string;
  content: string;
  role: "user" | "assistant" | "system";
  finish_reason?: string;
  prompt_tokens?: number;
  completion_tokens?: number;
  created_at?: string;
};

export type Assistant = {
  id: string;
  name: string;
  description: string;
  created_at: string;
};

export type Settings = {
  open_ai_secret: string;
  open_ai_model: string;
};

export type EmailCategory = {
  id: string;
  category_id: string;
  child_full_domains?: string; // Optional in Rust, so it's optional in TS
  name?: string;
  sender_company?: string;
  sender_domain?: string;
  avatar_link?: string; // Optional avatar link
  created_at: Date; // Corresponds to NaiveDateTime in Rust, use Date in TS
  tags?: string; // Optional field for tags
  latest_email_at?: Date; // Optional field for latest email timestamp
  description?: string; // Optional description
  priority?: number; // Optional priority, i32 in Rust corresponds to number in TS
  color?: string; // Optional hex color code
  unread_count?: number; // Optional unread email count
  last_accessed_at?: Date; // Optional last accessed timestamp
  is_archived?: boolean; // Optional archived flag
  custom_data?: string; // Optional custom data (arbitrary JSON)
  parent_category_id?: string; // Optional parent category ID
  visibility?: string; // Optional visibility setting
  is_synced?: boolean; // Optional sync status
};

interface Email {
  id: string; // Unique identifier for the email
  subject: string; // The subject of the email
  snippet: string; // A short snippet or preview of the email content
  from: string; // The sender of the email
  to: string; // Recipients' email addresses (as JSON string)
  cc?: string; // Email addresses in the CC field (as JSON string)
  bcc?: string; // Email addresses in the BCC field (as JSON string)
  date: Date; // The date and time when the email was sent or received
  category?: string; // The category of the email (e.g., 'Work', 'Personal', etc.)
  labels?: string; // Labels associated with the email (as JSON string)
  attachments?: string; // Identifiers or file names of attachments (as JSON string)
  attachment_types?: string; // Types of attachments (as CSV string)
  total_attachment_size?: number; // Total size of all attachments in bytes
  metadata_headers?: string; // Raw metadata headers
  email_body_url?: string; // URL to fetch the full email body
  is_read?: boolean; // Flag to indicate if the email has been read
  thread_id?: string; // ID of the conversation thread this email belongs to
  thread_summary?: string; // Summary of the conversation thread
  priority?: string; // The priority of the email (e.g., High Priority, Low Priority)
  urgency_score?: number; // Urgency score calculated based on email content
  sentiment?: string; // Sentiment of the email content
  actionable_items?: string; // List of actionable items identified in the email content (as JSON string)
  language?: string; // Language detected in the email content
  phishing_risk?: string; // Assessed phishing risk level of the email
  sender_reputation?: string; // Reputation level of the sender
  full_domain?: string; // Stores the full domain (e.g., info.twilio.com)
  main_domain?: string; // Stores the main domain
  storage_location?: string; // Indicates where the email data is stored (e.g., "cloud", "local")
  is_flagged?: boolean; // Flag to indicate if the email is marked for follow-up
  process_flag?: boolean; // Flag to indicate if the email needs further processing
  email_type?: string; // Type of email (e.g., Incoming, Outgoing, Reply, Forward)
  is_thread_root?: boolean; // Indicates if this is the root email of a thread
  received_as?: string; // Indicates how the email was received (e.g., to, cc, bcc)
  parent_email_id?: string; // Reference to the parent email in the thread
  read_receipt_url?: string; // URL to track if the email was opened
  reply_suggestion?: string; // AI-generated reply suggestions (as JSON string)
  follow_up_date?: Date; // Suggested follow-up date for the email
  meeting_proposed?: boolean; // Indicates if the email proposes a meeting
  meeting_link?: string; // Link to the meeting if scheduled
  is_delegated?: boolean; // Indicates if the email was delegated to another user
  task_status?: string; // Status of actionable tasks (e.g., Pending, Completed)
  auto_reply_sent?: boolean; // Indicates if an auto-reply was sent
  flagged_keywords?: string; // Important keywords flagged in the email (as JSON string)
  attachments_downloaded?: boolean; // Indicates if attachments were downloaded
  shared_with?: string; // List of users or groups the email is shared with (as JSON string)
  analytics_score?: number; // Custom score for analyzing email performance
  response_time?: number; // Time taken to respond to the email (in seconds)
  ai_generated?: boolean; // Indicates if the email content was AI-generated
  is_send?: boolean; // Indicates if the email content was AI-generated
  source_app?: string; // Source application (e.g., email, chat, notifications)
  created_at?: Date; // Record creation timestamp
  updated_at?: Date; // Record update timestamp
}

export type Attachment = {
  id: string;
  filename: string;
  mimeType: string;
  size: number;
};

export type User = {
  id: string; // Unique identifier for the user
  email: string; // User's email address
  verified_email: boolean; // Whether the user's email is verified
  name: string; // Full name of the user
  given_name: string; // First name
  family_name: string; // Last name
  picture: string; // Profile picture URL
  refreshToken: string; // Token to refresh user session
  expires_in: string; // Time until token expires
  access_token: string; // Access token for API requests

  // Suggested additional fields
  username: string; // Username or handle (if applicable)
  middle_name?: string; // Optional middle name
  gender?: string; // Gender (male, female, other)
  date_of_birth?: string; // Date of birth (ISO format)
  locale: string; // User's preferred language/locale (e.g., "en-US")
  timezone?: string; // User's time zone (e.g., "America/New_York")
  user_agent?: string; // Last used device or browser information
  ip_address?: string; // Last known IP address of the user

  // Activity Tracking
  created_at: string; // Date account was created
  updated_at: string; // Last update to the user profile
  last_login: string; // Date and time of the user's last login
  login_count: number; // Number of times the user has logged in

  // Security Information
  phone_number?: string; // Phone number for two-factor authentication (2FA)
  recovery_email?: string; // Backup recovery email
  security_question?: {
    question: string;
    answer: string;
  }; // Optional security question for account recovery

  // Address Information
  address?: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  // Role & Permissions
  role: string; // Role (e.g., admin, user, etc.)
  permissions?: string[]; // List of user permissions (e.g., "read", "write")
  is_active: boolean; // Boolean flag to indicate account is active

  // Notifications Preferences
  notification_preferences?: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
  }; // Notification preferences

  // Social Login Information
  social_accounts?: {
    google?: string;
    facebook?: string;
    twitter?: string;
  }; // External social logins

  // GDPR & Compliance
  gdpr_consent?: boolean; // Whether user consented to GDPR policies
  privacy_settings?: {
    data_sharing: boolean; // User preference for sharing data with third parties
    targeted_ads: boolean; // Whether user allows targeted ads
  };

  // Preferences
  theme_preference?: "light" | "dark"; // UI theme preference
  preferred_currency?: string; // Currency code for financial transactions (e.g., "USD")

  // Billing and Subscription Info (if applicable)
  billing_info?: {
    billing_address?: string; // Billing address for payments
    subscription_type?: string; // Subscription plan (e.g., "free", "premium")
    subscription_expiry?: string; // Subscription expiration date
  };

  // Custom Metadata
  custom_metadata?: Record<string, any>; // A field for storing any additional metadata
};

export class Caller {
  userId: number; // Corresponds to the `user` ForeignKey
  phoneNumber: string;
  name?: string; // Optional field
  callTime: Date;
  lastCallTime?: Date; // Optional field
  callCount: number = 1; // Default value
  averageCallDuration?: string; // Optional field, duration as ISO8601 string
  totalCallDuration: string = "PT0S"; // Default to 0 duration as ISO8601 string
  preferredContactTime?: string; // Optional field
  notes?: string; // Optional field

  constructor(data: Partial<Caller>) {
    Object.assign(this, data);
  }

  /**
   * Update call-related fields: call count, last call time, average duration, and total duration.
   */
  updateCallData(duration?: string): void {
    this.callCount += 1;
    this.lastCallTime = new Date();
    if (duration) {
      const totalDuration = this.parseDuration(this.totalCallDuration) + this.parseDuration(duration);
      this.totalCallDuration = this.formatDuration(totalDuration);
      this.averageCallDuration = this.formatDuration(totalDuration / this.callCount);
    }
  }

  /**
   * Parse ISO8601 duration string into seconds.
   */
  private parseDuration(duration: string): number {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;
    const [, hours, minutes, seconds] = match.map((val) => parseInt(val || "0", 10));
    return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
  }

  /**
   * Format seconds into ISO8601 duration string.
   */
  private formatDuration(seconds: number): string {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;
    return `PT${h ? `${h}H` : ""}${m ? `${m}M` : ""}${s ? `${s}S` : ""}`;
  }
}
export type Settings = {
  // OpenAI API settings
  open_ai_secret: string; // OpenAI API Secret Key
  open_ai_model: string; // Default OpenAI Model (e.g., GPT-3, GPT-4)

  // Email Settings
  smtp_server: string; // SMTP server for sending emails
  smtp_port: number; // Port number for the SMTP server
  smtp_username: string; // Username for SMTP authentication
  smtp_password: string; // Password for SMTP authentication
  default_email_sender: string; // Default sender email address

  // Notification Settings
  enable_notifications: boolean; // Enable or disable notifications globally
  notification_email: string; // Email to send notifications to
  notification_sms?: string; // Optional phone number for SMS notifications
  push_notification_api_key?: string; // API key for push notification service

  // User Interface Settings
  theme: "light" | "dark" | "system"; // UI theme preference (light, dark, or system default)
  default_language: string; // Default language for the application (e.g., "en", "es")

  // Security Settings
  enable_2fa: boolean; // Enable or disable two-factor authentication (2FA)
  session_timeout: number; // Time (in minutes) for session expiration
  allowed_ip_ranges?: string[]; // Array of allowed IP ranges for enhanced security

  // API and Integration Settings
  google_api_key?: string; // API Key for Google integrations (e.g., Calendar, Gmail)
  cloudinary_api_key?: string; // API Key for Cloudinary (for media uploads)
  stripe_secret_key?: string; // API key for Stripe (for payment processing)

  // Application Behavior Settings
  auto_save_interval: number; // Interval (in seconds) for auto-saving user progress
  max_upload_size: number; // Maximum upload size for file attachments (in bytes)
  data_retention_period: number; // Time (in days) to retain user data before auto-deletion

  // Logging and Debugging
  logging_level: "info" | "debug" | "error"; // Set logging level for the application
  enable_error_tracking: boolean; // Enable or disable error tracking
  error_tracking_service?: string; // Third-party error tracking service (e.g., Sentry)

  // Feature Toggles
  enable_experimental_features: boolean; // Enable experimental features for testing
  beta_access_enabled: boolean; // Enable access to beta features for certain users

  // GDPR & Compliance
  enable_gdpr_compliance: boolean; // Toggle GDPR compliance features
  data_export_enabled: boolean; // Enable or disable data export functionality

  // Custom Metadata
  custom_metadata?: Record<string, any>; // Additional customizable settings as key-value pairs
};

export type EmailContext = {
  full_name: string;
  job_title: string;
  organization: string;
  target_audience: string;
  communication_goal: string;
  call_to_action: string;
  tone_preference: string;
  language_style: string;
  key_points: string;
  known_preferences: string[];
  personal_sign_off: string;
  email_signature: string;
  work_hours: string[];
  availability_note: string;
  email_length_preference: string;
  urgency_level: string;
  email_length:number;
};

export type PhoneCallContext = {
  // User-specific context
  fullName: string;
  jobTitle: string;
  organization?: string;

  // Targeting and communication details
  targetAudience: string;
  communicationGoal: string;
  callToAction?: string;
  tonePreference: string;
  languageStyle: string;
  preferredResponsePatterns?: string[]; // Matches Option<Vec<String>>
  personalizationTriggers?: string[]; // Matches Option<Vec<String>>
  keyPoints: string;

  // Known caller preferences
  contextualData?: string[]; // Matches Option<Vec<String>>

  // Business details
  businessOverview?: string;
  pricingInfo?: string;
  frequentlyAskedQuestions?: string[]; // Matches Option<Vec<String>>
  servicesOffered?: string[]; // Matches Option<Vec<String>>

  // Availability and contact details
  workHours?: string[]; // Matches Option<(String, String)>
  availabilityNote?: string;
  contactEmail?: string;
  contactPhone?: string;
  locationAddress?: string;

  // AI agent configuration
  urgencyLevel: string;
  greetingMessage: string;
  followUpMessage?: string;
  dynamicScripts?: string[]; // Matches Option<Vec<String>>
  escalationContact?: string;
  supportedLanguages?: string[]; // Matches Option<Vec<String>>
  sentimentTriggers: string[]; // Matches Option<Vec<String>>
  additionalResources?: string;

  // Advanced conversational configurations
  adaptiveLearningEnabled: boolean;
  responseTimePreferences?: string;
  defaultEscalationPath?: string;
  backupAgentContact?: string;
};
