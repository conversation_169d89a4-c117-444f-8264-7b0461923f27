[package]
name = "Oway"
version = "0.0.1"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2", features = ["macos-private-api"] }
tokio = { version = "1.27.0", features = ["macros", "rt-multi-thread", "full"] }
tracing = "0.1.37"
tracing-subscriber = "0.3.16"
dirs = "5.0.0"
diesel = { version = "2.0.0", features = [
  "sqlite",
  "chrono",
  "serde_json",
  "64-column-tables",
  "r2d2",
] }
r2d2 = "0.8"
diesel_migrations = "2.0.0"
libsqlite3-sys = { version = ">=0.17.2, <0.26.0", features = ["bundled"] }
reqwest = { version = "0.11", features = ["json"] }
chrono = { version = "0.4.24", features = ["serde"] }
uuid = { version = "1.17.0", features = ["v4"] }
oauth2 = "4.4.2"
home = "0.5.5"
dotenv = "0.15.0"
url = "2.4.1"
timer = "0.2.0"
regex = "1.5.4"

##  path = "/Users/<USER>/Desktop/rust-bert" --> 0.22.0 works with torch 2.4.0
whatlang = "0.11.0"
# rust-bert = "0.21.0" # {path = "/Users/<USER>/Desktop/rust-bert"} this the working version
# tch = "0.13.0"  # or the version you're using
google-cloud-pubsub = "0.29.0"
google-cloud-auth = "0.17.0"
google-cloud-token = "0.1.2"
tokio-retry = "0.3.0"          # For retry mechanisms
tokio-util = "0.7"
futures = "0.3"
## https://github.com/LaurentMazare/tch-rs/issues/488
fix-path-env = { git = "https://github.com/tauri-apps/fix-path-env-rs" }
lazy_static = "1.4"
directories = "5.0.1"
base64 = "0.13"                                                          # or the latest version
mime_guess = "2.0.5"
rand = "0.9.0"
once_cell = "1.18"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-shell = "2"
tauri-plugin-dialog = "2"

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol"]
# this feature is used used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = ["tauri/custom-protocol"]


[build]
target = "aarch64-apple-darwin"
# target = "x86_64-pc-windows-msvc"
# rustflags = ["-C", "link-arg=-Wl,-rpath,/usr/local/libtorch/lib"]
# target = "aarch64-apple-darwin"
# rustflags = ["-C", "link-arg=-Wl,-rpath,/usr/local/libtorch/lib"]


#. export RUSTFLAGS="-C link-arg=-Wl,-rpath,/usr/local/libtorch/lib" && cargo build --target=aarch64-apple-darwin

# otool -l /Users/<USER>/Desktop/AI_Secartery/oway/src-tauri/target/aarch64-apple-darwin/debug/oway | grep -A 3 "LC_RPATH"

#  install_name_tool -add_rpath /usr/local/libtorch/lib /Users/<USER>/Desktop/AI_Secartery/oway/src-tauri/target/aarch64-apple-darwin/debug/oway
