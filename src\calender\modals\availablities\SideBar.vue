<template>
  <div class="w-1/4 h-full py-3 bg-primary border-r-2 border-[#e1e1e1]">
    <div class="flex justify-between items-center px-1">
      <button
        @click="() => emit('close')"
        class="size-8 flex justify-start items-center gap-1 text-secondary-500 hover:text-secondary-600 transition-all duration-200"
      >
        <ChevronLeftIcon class="size-7" />
      </button>
      <div class="text-lg font-semibold text-secondary-700">Availability</div>
      <div
        class="w-8 text-secondary-700 hover:text-secondary-900 cursor-pointer transition-all duration-300"
        @click="
          () => {
            emit('newSchedule');
            selSchedule = defaultSelSchedule;
            editableSch = { ...defaultSelSchedule };
          }
        "
      >
        <PencilIcon class="size-4" />
      </div>
    </div>
    <div class="flex flex-col gap-2 w-full h-auto px-1 mt-4">
      <div
        class="group flex justify-between items-center px-1 rounded-md w-full h-14 hover:bg-primary-300/50 transition-all duration-300 cursor-pointer"
        v-if="props.schedules"
        v-for="schedule in props.schedules"
        :class="
          editableSch && editableSch.id == schedule.id
            ? 'bg-secondary-300/80 border-l-4 border-l-dark-400 text-secondary-800'
            : 'bg-primary-300/30'
        "
        @click="
          () => {
            emit('selectSchedule', schedule);
            selectSchedule(schedule);
          }
        "
      >
        <div class="flex flex-col gap-1">
          <div>{{ schedule.name }}</div>
          <div class="flex items-center justify-between w-full gap-4">
            <div class="flex gap-1 items-center text-xs">
              <GlobeAltIcon class="size-4" />
              {{ schedule.timeZone }}
            </div>
            <div
              class="text-xs bg-dark-400 text-white px-1 py-[1px] rounded"
              v-if="session.calInfo?.defaultScheduleId === schedule.id"
            >
              default
            </div>
          </div>
        </div>
        <div class="group-hover:flex hidden h-full items-center w-7">
          <button
            @click="() => emit('deleteSchedule', schedule)"
            class="bg-red-200 p-1 rounded-lg text-red-600 hover:bg-red-300 transition-all duration-300"
          >
            <TrashIcon class="size-5" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { CalSchedule, defaultSelSchedule } from "../../../models/schedule-model";
import { ChevronLeftIcon, GlobeAltIcon, PencilIcon, TrashIcon } from "@heroicons/vue/24/outline";

const session = useCurrentUserStore();

const props = defineProps({
  schedules: {
    type: Object,
  },
});

const emit = defineEmits(["close", "selectSchedule", "deleteSchedule", "newSchedule"]);

const editableSch = ref<CalSchedule>();
const selSchedule = ref<CalSchedule>();

function selectSchedule(s: CalSchedule) {
  selSchedule.value = s;
  editableSch.value = { ...s };
}
</script>

<style scoped></style>
