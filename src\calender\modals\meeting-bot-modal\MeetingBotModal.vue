<template>
  <div class="size-full">
    <div class="flex justify-between h-8">
      <div class="flex items-center text-secondary-600 gap-0.5 h-8 px-1">
        <Icon icon="fluent:bot-48-filled" class="size-4 mb-0.5" />
        <p>Meeting Bot</p>
      </div>
      <button
        @click="emit('close')"
        class="text-secondary-600 hover:text-red-500 transition-colors duration-200 rounded-md size-8 flex justify-center items-center"
      >
        <XMarkIcon class="size-5 f" />
      </button>
    </div>
    <div class="flex flex-col p-2 gap-3">
      <div class="flex flex-col gap-1">
        <div>Platform</div>
        <ToggleGroups :items="platforms" :selected="selectedPlatform" @update:selected="selectedPlatform = $event" />
      </div>
      <div class="flex flex-col gap-1">
        <div>Link</div>
        <input
          type="text"
          name="link"
          class="border-none rounded-md bg-secondary-500/20"
          :placeholder="
            selectedPlatform == MeetingPlatform.MEET ? 'https://meet.google.com/...' : 'https://teams.microsoft.com/...'
          "
          v-model="link"
        />
      </div>
      <div class="flex flex-col gap-1">
        <div>Description</div>
        <textarea
          type="text"
          name="link"
          class="border-none rounded-md bg-secondary-500/20"
          placeholder="Meeting with client..."
          v-model="description"
        />
      </div>
      <div class="flex justify-end mt-2">
        <button
          @click="joinMeeting()"
          class="flex items-center h-9 px-7 font-semibold bg-secondary-600 hover:bg-secondary-700 gap-2 text-white rounded-md"
        >
          <div>Join</div>
          <PlayIcon class="size-4 mt-0.5" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { Icon } from "@iconify/vue/dist/iconify.js";
import ToggleGroups, { ToggleGroupsItem } from "../../../components/ui/ToggleGroups.vue";
import { ref } from "vue";
import { PlayIcon } from "@heroicons/vue/24/solid";
import { CreateMeetingRecording, MeetingPlatform, MeetingStatus } from "../../../models/meetings-modal";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { MeetingBotService } from "../../../services/meeting-bot-service";

const emit = defineEmits(["close"]);

const session = useCurrentUserStore();

const platforms: ToggleGroupsItem[] = [
  {
    label: "Google Meet",
    value: "meet",
  },
  {
    label: "Windows Teams",
    value: "teams",
  },
];

const selectedPlatform = ref<MeetingPlatform>(MeetingPlatform.MEET);
const link = ref<string | null>(null);
const description = ref<string | null>(null);

async function joinMeeting() {
  try {
    //  console.log("calinfo", session.calInfo);
    if (!link) throw new Error("Link is required");
    if (!session.calInfo?.id) throw new Error("Calendar ID is required");

    const newMeetingBot: CreateMeetingRecording = {
      platform: selectedPlatform.value,
      url: link.value!,
      description: description.value,
      userId: session.calInfo.id,
      recordingStatus: MeetingStatus.IN_PROGRESS,
      summarizationStatus: MeetingStatus.NOT_STARTED,
    };

    const result = await MeetingBotService.startMeetingBot(newMeetingBot);
    //  console.log("[join meeting] result", result);
    //  console.log("newMeetingBot", newMeetingBot);
  } catch (error: any) {
    console.error(error.message);
  }
}
</script>

<style scoped></style>
