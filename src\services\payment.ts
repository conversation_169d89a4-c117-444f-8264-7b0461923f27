const FIVE_DAYS_MS = 5 * 24 * 60 * 60 * 1000;

export const checkPaymentStatus = async (email: string): Promise<boolean> => {
  const lastChecked = localStorage.getItem("oway_last_checked");
  const now = Date.now();

  if (lastChecked && now - parseInt(lastChecked) < FIVE_DAYS_MS) {
    return localStorage.getItem("oway_paid") === "true";
  }

  try {
    const res = await fetch(`http://localhost:9000/api/check-status/?email=${email}`);
    const data = await res.json();

    const isPaid = data.is_paid === true;
    if (isPaid) {
      localStorage.setItem("oway_paid", "true");
    } else {
      localStorage.removeItem("oway_paid");
    }

    localStorage.setItem("oway_last_checked", now.toString());
    return isPaid;
  } catch (err) {
    console.error("❌ Error checking payment status:", err);
    return false;
  }
};