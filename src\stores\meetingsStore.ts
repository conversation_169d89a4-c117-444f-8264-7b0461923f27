import { defineStore } from "pinia"
import { MeetingsRecordings } from "../models/meetings-modal";
import { MeetingBotService } from "../services/meeting-bot-service";

export const useMeetingsStore = defineStore("meetingsStore", {
  state() {
    return {
      meetings: [] as MeetingsRecordings[],
      selectedMeeting: null as MeetingsRecordings | null,
    }
  },
  actions: {
    async refresh() {
      const meetings = await MeetingBotService.getMeetings();
      if (!meetings) return;
      this.meetings = meetings;
    },
    setSelectedMeeting(meeting: MeetingsRecordings | null) {
      //  console.log("setSelectedMeeting", meeting);
      this.selectedMeeting = meeting;
    }


  }
})
